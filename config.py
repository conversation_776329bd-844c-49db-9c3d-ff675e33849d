import os
from dotenv import load_dotenv
from typing import Optional

# 尝试加载多个可能的环境配置文件
for env_file in ['.env', 'config.env']:
    if os.path.exists(env_file):
        load_dotenv(env_file)
        break

class Config:
    #============================================
    # 服务器配置 / Server Configuration
    #============================================
    SERVER_HOST: str = os.getenv("SERVER_HOST", "0.0.0.0")
    SERVER_PORT: int = int(os.getenv("SERVER_PORT", "8000"))
    
    #============================================
    # AI模型配置 / AI Model Configuration
    #============================================
    # 本地AI模型服务配置
    LOCAL_MODEL_BASE_URL: str = os.getenv("LOCAL_MODEL_BASE_URL", "http://localhost:8001/v1")
    LOCAL_MODEL_NAME: str = os.getenv("LOCAL_MODEL_NAME", "deepseek-r1:32b")
    LOCAL_MODEL_API_KEY: str = os.getenv("LOCAL_MODEL_API_KEY", "token-abc123")
    
    # 向后兼容的vLLM配置
    VLLM_BASE_URL: str = os.getenv("VLLM_BASE_URL", LOCAL_MODEL_BASE_URL)
    VLLM_MODEL: str = os.getenv("VLLM_MODEL", LOCAL_MODEL_NAME)
    VLLM_API_KEY: str = os.getenv("VLLM_API_KEY", LOCAL_MODEL_API_KEY)
    
    # AI模型请求配置
    MODEL_MAX_TOKENS: int = int(os.getenv("MODEL_MAX_TOKENS", "4000"))
    MODEL_TEMPERATURE: float = float(os.getenv("MODEL_TEMPERATURE", "0.3"))
    
    #============================================
    # 安全分级配置 / Security Level Configuration
    #============================================
    # 安全级别: high=高安全(限制AI), low=低安全(AI自主)
    SECURITY_LEVEL: str = os.getenv("SECURITY_LEVEL", "low").lower()
    
    # 预设策略模式开关
    ENABLE_PREDEFINED_STRATEGIES: bool = os.getenv("ENABLE_PREDEFINED_STRATEGIES", "true").lower() == "true"
    
    @property
    def is_high_security(self) -> bool:
        """判断是否为高安全级别"""
        return self.SECURITY_LEVEL == "high"
    
    @property
    def use_predefined_strategies(self) -> bool:
        """判断是否使用预设策略"""
        return self.is_high_security and self.ENABLE_PREDEFINED_STRATEGIES
    
    #============================================
    # SSH连接配置 / SSH Connection Configuration
    #============================================
    SSH_CONNECT_TIMEOUT: int = int(os.getenv("SSH_CONNECT_TIMEOUT", "30"))
    SSH_COMMAND_TIMEOUT: int = int(os.getenv("SSH_COMMAND_TIMEOUT", "60"))
    SSH_MAX_RETRIES: int = int(os.getenv("SSH_MAX_RETRIES", "3"))
    SSH_RETRY_DELAY: int = int(os.getenv("SSH_RETRY_DELAY", "2"))
    
    #============================================
    # 诊断配置 / Diagnosis Configuration
    #============================================
    MAX_DIAGNOSIS_ATTEMPTS: int = int(os.getenv("MAX_DIAGNOSIS_ATTEMPTS", "5"))
    DIAGNOSIS_LOG_RETENTION_DAYS: int = int(os.getenv("DIAGNOSIS_LOG_RETENTION_DAYS", "30"))
    ENABLE_DETAILED_DIAGNOSIS_LOG: bool = os.getenv("ENABLE_DETAILED_DIAGNOSIS_LOG", "true").lower() == "true"
    
    #============================================
    # 系统配置 / System Configuration
    #============================================
    # 服务信息文件路径
    SERVICE_INFO_FILE: str = os.getenv("SERVICE_INFO_FILE", "service_info.txt")
    
    # 日志级别
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # 调试模式
    DEBUG_MODE: bool = os.getenv("DEBUG_MODE", "false").lower() == "true"
    
    # 工作目录
    WORK_DIR: str = os.getenv("WORK_DIR", "/tmp/ops_agent")
    
    #============================================
    # 微信机器人配置 / WeChat Bot Configuration
    #============================================
    ENABLE_WECHAT_BOT: bool = os.getenv("ENABLE_WECHAT_BOT", "false").lower() == "true"
    
    # 向后兼容
    WECHAT_BOT_ENABLED: bool = os.getenv("WECHAT_BOT", "FALSE").upper() == "TRUE" or ENABLE_WECHAT_BOT
    
    WECHAT_WEBHOOK_URL: str = os.getenv("WECHAT_WEBHOOK_URL", "")
    WECHAT_BOT_URL: str = os.getenv("WECHAT_BOT_URL", WECHAT_WEBHOOK_URL)  # 向后兼容
    WECHAT_BOT_SECRET: str = os.getenv("WECHAT_BOT_SECRET", "")
    
    #============================================
    # 性能配置 / Performance Configuration
    #============================================
    MAX_CONCURRENT_DIAGNOSES: int = int(os.getenv("MAX_CONCURRENT_DIAGNOSES", "3"))
    API_RATE_LIMIT_PER_MINUTE: int = int(os.getenv("API_RATE_LIMIT_PER_MINUTE", "60"))
    MEMORY_LIMIT_MB: int = int(os.getenv("MEMORY_LIMIT_MB", "512"))
    
    #============================================
    # 配置验证 / Configuration Validation
    #============================================
    @classmethod
    def validate(cls):
        """验证必要的配置"""
        # 验证AI模型配置
        if not cls.LOCAL_MODEL_BASE_URL and not cls.VLLM_BASE_URL:
            raise ValueError("LOCAL_MODEL_BASE_URL or VLLM_BASE_URL is required for AI model operations")
        
        if not cls.LOCAL_MODEL_NAME and not cls.VLLM_MODEL:
            raise ValueError("LOCAL_MODEL_NAME or VLLM_MODEL is required for AI model operations")
        
        # 验证安全级别
        if cls.SECURITY_LEVEL not in ['high', 'low']:
            raise ValueError("SECURITY_LEVEL must be 'high' or 'low'")
        
        # 验证端口范围
        if not (1 <= cls.SERVER_PORT <= 65535):
            raise ValueError("SERVER_PORT must be between 1 and 65535")
        
        # 验证超时配置
        if cls.SSH_CONNECT_TIMEOUT <= 0 or cls.SSH_COMMAND_TIMEOUT <= 0:
            raise ValueError("SSH timeout values must be positive")
        
        # 创建工作目录
        if not os.path.exists(cls.WORK_DIR):
            try:
                os.makedirs(cls.WORK_DIR, exist_ok=True)
            except Exception as e:
                print(f"Warning: Cannot create work directory {cls.WORK_DIR}: {e}")
        
        return True
    
    @classmethod
    def get_security_info(cls) -> dict:
        """获取安全配置信息"""
        return {
            "security_level": cls.SECURITY_LEVEL,
            "is_high_security": cls.is_high_security,
            "use_predefined_strategies": cls.use_predefined_strategies,
            "ai_autonomy": "Limited" if cls.is_high_security else "Full"
        }

config = Config() 
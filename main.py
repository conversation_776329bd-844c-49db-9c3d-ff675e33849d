#!/usr/bin/env python3
"""
AI运维代理主程序
提供自动诊断和修复Docker服务问题的能力
"""

import uvicorn
import asyncio
import signal
import sys
from loguru import logger
from contextlib import asynccontextmanager

from config import config
from api_server import app, monitor_service
from ssh_mcp_client import ssh_mcp_client
from model_client import aiops_client

def setup_logging():
    """设置日志配置"""
    logger.remove()  # 移除默认处理器
    
    # 添加控制台日志
    logger.add(
        sys.stdout,
        level=config.LOG_LEVEL,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # 添加文件日志
    logger.add(
        "logs/app.log",
        level=config.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days",
        compression="zip"
    )

@asynccontextmanager
async def lifespan(app):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("AI运维代理启动中...")
    
    # 验证配置
    try:
        config.validate()
        logger.info("✅ 配置验证通过")
    except Exception as e:
        logger.error(f"❌ 配置验证失败: {e}")
        raise
    
    # 测试模型连接
    await test_model_connections()
    
    # 测试MCP连接
    await test_mcp_connection()
    
    # 加载服务信息
    await load_initial_services()
    
    # 启动监控服务
    await start_monitoring_service()
    
    logger.info("AI运维代理启动完成")
    logger.info(f"API服务地址: http://{config.SERVER_HOST}:{config.SERVER_PORT}")
    
    # 显示安全配置信息
    security_info = config.get_security_info()
    logger.info(f"🔐 安全配置: {security_info['security_level'].upper()} 级别")
    logger.info(f"🤖 AI自主权: {security_info['ai_autonomy']}")
    if security_info['is_high_security']:
        logger.info("⚠️  高安全模式: AI仅限使用预定义策略")
    else:
        logger.info("🚀 自主模式: AI拥有完全创新决策权")
    
    logger.info("可用的API端点:")
    logger.info("   POST /diagnose - 诊断服务错误")
    logger.info("   POST /health-check - 服务健康检查")
    logger.info("   POST /service/operation - 统一服务操作 (docker-compose/supervisor)")
    logger.info("   GET  /service/{service_name}/info - 获取服务配置信息")
    logger.info("   GET  /services - 获取服务列表")
    logger.info("   GET  /history - 获取诊断历史")
    logger.info("   POST /manual-command - 手动执行命令")
    logger.info("   GET  /security/config - 获取安全配置")
    logger.info("   POST /security/config - 更新安全配置 (运行时)")
    logger.info("   POST /wechat/notify - 发送微信MCP通知")
    logger.info("   GET  /wechat/test - 测试微信MCP连接")
    logger.info("   POST /monitor/add - 添加服务到监控")
    logger.info("   POST /monitor/remove - 从监控移除服务")
    logger.info("   GET  /monitor/status - 获取监控状态")
    logger.info("   POST /monitor/start - 启动监控")
    logger.info("   POST /monitor/stop - 停止监控")
    
    yield
    
    # 关闭时执行
    logger.info("AI运维代理关闭中...")
    
    # 停止监控服务
    await stop_monitoring_service()
    
    # 清理连接（SSH MCP客户端会自动管理连接）
    logger.info("✅ 资源已清理")
    
    logger.info("AI运维代理已关闭")

async def test_model_connections():
    """测试本地模型连接"""
    logger.info("测试本地模型连接...")
    
    try:
        test_response = await aiops_client.chat_completion([
            {"role": "user", "content": "Hello, this is a test message./no_think"}
        ])
        if test_response:
            logger.info("✅ 本地模型连接正常")
        else:
            logger.warning("⚠️ 本地模型连接可能有问题")
    except Exception as e:
        logger.warning(f"⚠️ 本地模型连接失败: {e}")
        logger.info("请确保vLLM服务已正常启动并监听在配置的端口上")

async def test_mcp_connection():
    """测试MCP连接"""
    logger.info("测试微信MCP连接...")
    
    try:
        from wechat_mcp_client import wechat_mcp_client
        
        if wechat_mcp_client.enabled:
            logger.info("✅ 微信MCP服务可用")
            # 可选：发送启动通知
            # success = await wechat_mcp_client.send_text_message("🚀 AI运维代理已启动")
            # if success:
            #     logger.info("✅ MCP启动通知发送成功")
        else:
            logger.warning("⚠️ 微信MCP服务不可用")
            logger.info("请检查wechat_mcp目录和uv环境")
    except Exception as e:
        logger.warning(f"⚠️ 微信MCP连接测试失败: {e}")

async def load_initial_services():
    """加载初始服务信息"""
    try:
        # 使用SSH MCP客户端获取服务列表
        services_result = await ssh_mcp_client.list_services()
        
        if services_result.get("success", False) and "services" in services_result:
            services = services_result["services"]
            logger.info(f"已加载 {len(services)} 个服务配置")
            
            for service in services:
                service_name = service.get("name", "未知")
                directory = service.get("directory", "N/A")
                logger.info(f"   - {service_name}: {directory}")
        else:
            logger.warning("⚠️ 未能获取服务配置信息")
            logger.info("请检查service_info.txt文件和SSH MCP服务")
            
    except Exception as e:
        logger.error(f"❌ 加载服务信息失败: {e}")

async def start_monitoring_service():
    """启动监控服务"""
    try:
        logger.info("启动监控服务...")
        
        # 加载监控配置
        load_result = monitor_service.load_services_from_config()
        if load_result:
            services_count = len(monitor_service.services)
            logger.info(f"✅ 监控服务已加载 {services_count} 个服务配置")
            
            # 启动监控线程
            monitor_service.start_monitoring()
            logger.info("✅ 监控服务已启动，开始持续监控服务健康状态")
            
            # 显示监控的服务列表
            for service_name, service_info in monitor_service.services.items():
                logger.info(f"   📊 监控中: {service_name} ({service_info['url']})")
        else:
            logger.warning("⚠️ 监控服务配置加载失败")
            
    except Exception as e:
        logger.error(f"❌ 启动监控服务失败: {e}")

async def stop_monitoring_service():
    """停止监控服务"""
    try:
        logger.info("停止监控服务...")
        monitor_service.stop_monitoring()
        logger.info("✅ 监控服务已停止")
    except Exception as e:
        logger.error(f"❌ 停止监控服务失败: {e}")

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在优雅关闭...")
    sys.exit(0)

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("AI运维代理 v1.0.0")
    logger.info("==========================================")
    
    # 将生命周期管理器附加到app
    app.router.lifespan_context = lifespan
    
    # 启动API服务器
    try:
        uvicorn.run(
            app,
            host=config.SERVER_HOST,
            port=config.SERVER_PORT,
            log_config=None,  # 使用我们自己的日志配置
            access_log=False  # 禁用访问日志，避免重复
        )
    except Exception as e:
        logger.error(f"❌ 服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

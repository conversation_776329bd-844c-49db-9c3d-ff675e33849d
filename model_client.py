import httpx
from typing import List, Dict, Any, Optional
from loguru import logger
from config import config

class LocalModelClient:
    """完全本地化的AI运维模型客户端"""
    
    def __init__(self):
        self.base_url = config.VLLM_BASE_URL
        self.model = config.VLLM_MODEL
        self.api_key = config.VLLM_API_KEY
    
    def _get_restricted_system_prompt(self) -> str:
        """获取限制模式的系统提示"""
        return """# Role: 高级服务运维专家（安全限制模式）

## Background:
我是一位资深的运维专家，专注于服务诊断和故障排除。在当前安全限制模式下，我只能使用预定义的故障诊断策略，确保运维操作的安全性和可控性。

## Operating Mode: 
🔒 **安全限制模式** - 只能选择预定义的诊断策略

## Predefined Strategies:
### 策略1: 基础服务重启
- 适用场景: 服务无响应、进程异常
- 操作步骤: 停止服务 -> 检查进程 -> 重新启动服务

### 策略2: 端口冲突解决
- 适用场景: 端口被占用、绑定失败
- 操作步骤: 检查端口占用 -> 释放端口 -> 重启服务

### 策略3: 配置文件检查
- 适用场景: 启动失败、配置错误
- 操作步骤: 检查配置语法 -> 备份配置 -> 重载配置

### 策略4: 日志分析重启
- 适用场景: 错误日志明确、故障模式已知
- 操作步骤: 分析错误日志 -> 清理日志 -> 重启服务

### 策略5: 系统资源检查
- 适用场景: 性能问题、资源不足
- 操作步骤: 检查CPU/内存/磁盘 -> 清理资源 -> 重启服务

## Service Types Support:
### Docker Compose 服务:
- 命令前缀: `sudo bash -c "cd /path && docker-compose ..."`

### Supervisor 服务:
- 命令路径: `sudo /home/<USER>/anaconda3/bin/supervisorctl`

## Output Format:
请严格按照以下JSON格式输出：
{
    "strategy_used": "使用的预定义策略编号",
    "diagnosis": "基于选定策略的问题分析",
    "solution": "选定策略的解决方案",
    "commands": ["策略对应的命令列表"],
    "reasoning": "选择该策略的理由"
}

## Constraints:
⚠️ 安全限制：
- 必须从上述5个预定义策略中选择一个
- 不允许创造新的解决方案
- 不允许执行未定义的命令
- 必须说明选择策略的理由"""
    
    def _get_autonomous_system_prompt(self) -> str:
        """获取自主模式的系统提示"""
        return """# Role: 高级服务运维专家（完全自主模式）

## Background:
我是一位资深的运维专家，擅长处理复杂的服务问题，包括Docker容器服务和Supervisor管理的Python服务。在完全自主模式下，我具备持续分析和迭代解决问题的能力，能够根据历史尝试记录调整诊断策略，避免重复失败的方案。

## Operating Mode:
🚀 **完全自主模式** - 拥有完全的创新决策权

## Profile:
- language: 中文
- description: 我是一位具有丰富经验的高级运维工程师，专门处理需要多次尝试才能解决的复杂服务故障，包括Docker和Supervisor服务

## Goals:
1. 基于历史诊断尝试的结果，深度分析之前方案失败的原因
2. 从不同角度重新审视问题，提供创新的诊断思路和解决方案
3. 自主决定最佳的操作策略，不受预设模式限制
4. 确保问题能够得到根本性解决

## Service Types Support:
### Docker Compose 服务:
- 命令前缀: `sudo bash -c "cd /path && docker-compose ..."`
- 常用操作: start, stop, restart, ps, logs, down, up -d
- 典型问题: 端口冲突、容器依赖、镜像问题、网络问题

### Supervisor 服务:
- 命令路径: `sudo /home/<USER>/anaconda3/bin/supervisorctl`
- 常用操作: start, stop, restart, status, tail, reread, update
- 典型问题: 进程死锁、配置错误、Python环境问题、端口占用

## Intelligent Analysis Principles:
1. **深度根因分析**: 不满足于表面现象，要找到问题的根本原因
2. **多维度思考**: 从进程、网络、配置、权限、依赖、系统资源等角度分析
3. **创新解决思路**: 根据具体情况创造性地组合不同的解决方案
4. **避免重复失败**: 仔细分析历史记录，绝不重复已经失败的操作
5. **渐进式策略**: 从简单到复杂，从保守到激进，逐步升级解决方案

## Analysis Dimensions:
1. **服务层面**: 服务状态、进程状态、重启历史
2. **网络层面**: 端口占用、连接状态、防火墙规则
3. **配置层面**: 配置文件、环境变量、启动参数
4. **系统层面**: 资源使用、磁盘空间、内存状态
5. **依赖层面**: 数据库连接、外部API、文件依赖
6. **权限层面**: 文件权限、用户权限、系统权限

## Output Format:
请严格按照以下JSON格式输出智能诊断结果：
{
    "diagnosis": "基于历史分析的深度诊断，指出新的问题角度和可能根因",
    "solution": "创新的解决方案，说明与之前不同的解决思路",
    "commands": ["具体的可执行命令，基于服务类型使用正确的命令格式"],
    "reasoning": "详细说明方案选择的理论依据，与历史方案的本质区别，预期效果"
}

## Command Format Examples:
### Docker Compose:
- `sudo bash -c "cd /path && docker-compose ps"`
- `sudo bash -c "cd /path && docker-compose restart service"`
- `sudo bash -c "cd /path && docker-compose logs --tail=50"`

### Supervisor:
- `sudo /home/<USER>/anaconda3/bin/supervisorctl status service_name`
- `sudo /home/<USER>/anaconda3/bin/supervisorctl restart service_name`
- `sudo /home/<USER>/anaconda3/bin/supervisorctl tail service_name`

## Innovation Mandate:
🎯 **完全自主权**: 你有完全的创新决策权，可以：
- 设计全新的诊断策略
- 组合不同的技术手段
- 探索非常规的解决方案  
- 基于实际情况自主调整方案
- 不受任何预设策略限制

请根据服务类型和具体问题，自主设计最佳的诊断和修复策略。"""
    
    async def chat_completion(self, messages: List[Dict[str, str]], 
                            temperature: float = 0.7) -> Optional[str]:
        """发送聊天完成请求到本地模型"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    json={
                        "model": self.model,
                        "messages": messages,
                        "temperature": temperature,
                        "max_tokens": 2048
                    },
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    },
                    timeout=30.0
                )
                response.raise_for_status()
                data = response.json()
                return data["choices"][0]["message"]["content"]
        except Exception as e:
            logger.error(f"本地模型API调用失败: {e}")
            return None

    async def diagnose_error(self, service_name: str, server_ip: str, 
                           error_info: str, logs: str = "") -> Optional[str]:
        """使用本地模型诊断服务错误"""
        messages = [
            {
                "role": "system",
                "content": """# Role: 高级服务运维诊断专家

## Background:
我是一位在服务器运维领域工作多年的专家，专注于各类服务的诊断和故障排除，包括Docker容器服务和Supervisor管理的Python服务。我熟悉各类服务的部署架构和常见问题，对Linux系统运维和多种服务管理技术有深入了解。

## Profile:
- language: 中文
- description: 我是一位专业的服务运维工程师，专注于为各种服务故障提供快速准确的诊断和解决方案，支持Docker和Supervisor等多种服务类型

## Goals:
根据用户提供的服务错误信息和日志，快速分析问题根源，制定有效的解决方案。
输出完整的诊断结果和可执行的修复命令，确保服务能够快速恢复正常运行。
输出的诊断结果必须准确、清晰、可操作性强。

## Service Types Support:
### Docker Compose 服务:
- 命令前缀: `sudo bash -c "cd /path && docker-compose ..."`
- 常用操作: start, stop, restart, ps, logs, down, up -d
- 典型问题: 端口冲突、容器依赖、镜像问题、网络问题

### Supervisor 服务:
- 命令路径: `sudo /home/<USER>/anaconda3/bin/supervisorctl`
- 常用操作: start, stop, restart, status, tail, reread, update
- 典型问题: 进程死锁、配置错误、Python环境问题、端口占用

## Constrains:
1. 对于不在知识库中的特定错误，明确告知用户需要更多信息
2. 基于服务类型选择正确的命令格式和故障模式分析
3. 你必须提供可直接执行的Linux命令，包含必要的sudo权限和路径切换
4. 分析要考虑服务依赖、网络配置、资源限制、权限问题等多个维度

## Skills:
1. 具有强大的多类型服务诊断能力
2. 熟悉Docker Compose和Supervisor服务的部署和配置
3. 对Linux系统管理和网络配置有深入了解
4. 拥有结构化的问题分析思路，会利用JSON格式、缩进、分隔等来组织输出信息

## Output Format:
请严格按照以下JSON格式输出诊断结果：
{
    "diagnosis": "详细的问题诊断分析，包括问题类型、可能原因、影响范围",
    "solution": "具体的解决方案描述，说明解决思路和步骤",
    "commands": ["完整的可执行命令列表，每个命令独立，包含必要的sudo和cd操作"]
}

## Command Requirements:
⚠️ 命令格式严格要求：
- 所有操作必须添加sudo前缀
- 需要在特定目录执行的命令，使用格式：sudo bash -c "cd /path/to/directory && command"
- 每个命令必须单独列出，不要使用&&或;连接多个命令
- 如果涉及进程ID等参数，使用占位符说明，如"<container_id>"或"<service_name>"

### Docker Compose 命令示例：
- "sudo docker ps -a" (查看所有容器)
- "sudo bash -c \"cd /opt/service && docker-compose down\"" (停止服务)
- "sudo bash -c \"cd /opt/service && docker-compose up -d\"" (启动服务)

### Supervisor 命令示例：
- "sudo /home/<USER>/anaconda3/bin/supervisorctl status" (查看服务状态)
- "sudo /home/<USER>/anaconda3/bin/supervisorctl restart <service_name>" (重启服务)
- "sudo /home/<USER>/anaconda3/bin/supervisorctl tail <service_name>" (查看日志)

## Examples:
当遇到端口冲突问题时：
{
    "diagnosis": "检测到端口冲突错误，可能是目标端口已被其他进程占用，或者服务重启时端口未正确释放",
    "solution": "首先检查端口占用情况，停止冲突进程或服务，然后重新启动目标服务",
    "commands": [
        "sudo netstat -tulpn | grep :8080",
        "sudo bash -c \"cd /opt/service && docker-compose ps\"",
        "sudo bash -c \"cd /opt/service && docker-compose restart\""
    ]
}"""
            },
            {
                "role": "user",
                "content": f"""请诊断以下服务问题：

服务名称: {service_name}
服务器IP: {server_ip}
错误信息: {error_info}
相关日志: {logs}

请根据错误信息和日志内容判断服务类型（Docker Compose或Supervisor），
并按照指定的JSON格式提供完整的诊断结果和解决方案。"""
            }
        ]
        
        return await self.chat_completion(messages, temperature=0.3)
    
    async def diagnose_error_with_context(self, service_name: str, server_ip: str, 
                                        initial_error: str, diagnosis_context: str,
                                        attempt_number: int, strategy_hint: str = None) -> Optional[str]:
        """使用历史上下文进行持续诊断"""
        
        # 根据安全级别选择不同的system prompt
        if config.is_high_security:
            system_content = self._get_restricted_system_prompt()
            user_instructions = f"""请基于历史诊断记录，从5个预定义策略中选择一个：

服务名称: {service_name}
服务器IP: {server_ip}
初始错误: {initial_error}
当前尝试次数: {attempt_number}

=== 完整诊断上下文 ===
{diagnosis_context}

要求：
1. **策略选择**: 从5个预定义策略中选择最适合的一个
2. **安全合规**: 严格遵守安全限制，不能偏离预设策略
3. **理由说明**: 详细说明选择该策略的理由
4. **命令规范**: 使用预定义策略对应的标准命令

请选择最适合的预定义策略并说明理由！"""
        else:
            system_content = self._get_autonomous_system_prompt()
            user_instructions = f"""请基于历史诊断记录进行深度分析：

服务名称: {service_name}
服务器IP: {server_ip}
初始错误: {initial_error}
当前尝试次数: {attempt_number}

=== 完整诊断上下文 ===
{diagnosis_context}

请根据以上信息进行智能分析和诊断，要求：
1. **自主分析**: 不受预设策略限制，根据实际情况制定最佳方案
2. **深度根因**: 深入分析历史失败原因，找到问题根源
3. **创新思路**: 从新的角度提供与之前完全不同的解决方案
4. **精准执行**: 根据服务类型(Docker/Supervisor)使用正确的命令格式
5. **理论支撑**: 详细说明方案的理论依据和预期效果

请发挥你的专业能力，自主设计最优的诊断和修复策略！"""
        
        messages = [
            {
                "role": "system",
                "content": system_content
            },
            {
                "role": "user",
                "content": user_instructions
            }
        ]
        
        return await self.chat_completion(messages, temperature=0.4)

    async def parse_service_info(self, service_info_line: str) -> Optional[Dict[str, str]]:
        """解析服务信息行"""
        messages = [
            {
                "role": "system",
                "content": """# Role: 配置信息解析专家

## Background:
我是一位专业的配置管理专家，负责解析和处理各种服务配置信息。

## Goals:
准确解析服务配置信息，确保数据格式的正确性和完整性。

## Format:
输入格式：服务名,用户名,密码,目录路径
输出格式：严格的JSON格式

请以JSON格式返回解析结果：
{
    "service_name": "服务名",
    "username": "用户名", 
    "password": "密码",
    "directory": "目录路径"
}"""
            },
            {
                "role": "user",
                "content": f"请解析这行服务配置信息: {service_info_line}"
            }
        ]
        
        return await self.chat_completion(messages, temperature=0.1)
    
    async def generate_ssh_command(self, operation: str, service_name: str, 
                                 directory: str) -> Optional[str]:
        """生成SSH命令"""
        messages = [
            {
                "role": "system",
                "content": """# Role: SSH命令生成专家

## Background:
我是一位系统管理专家，专门负责生成安全可靠的SSH远程操作命令。

## Goals:
根据操作类型、服务名称和目录路径，生成对应的Docker操作命令。
确保命令的安全性、准确性和可执行性。

## Command Types:
- check_status: 检查服务状态
- restart: 重启服务
- stop: 停止服务  
- start: 启动服务
- logs: 查看日志
- ps: 查看容器列表

## Requirements:
- 所有Docker命令必须包含sudo权限
- 需要目录切换的命令使用完整路径格式
- 只返回具体的命令，不包含其他说明文字

## Examples:
操作类型: check_status -> sudo docker ps
操作类型: restart -> sudo bash -c "cd /path && docker-compose restart"
操作类型: logs -> sudo bash -c "cd /path && docker-compose logs --tail=50"
"""
            },
            {
                "role": "user",
                "content": f"""请生成Docker操作命令：

操作类型: {operation}
服务名称: {service_name}
工作目录: {directory}

请直接返回对应的命令。"""
            }
        ]
        
        return await self.chat_completion(messages, temperature=0.3)

# 全局本地模型客户端实例
local_client = LocalModelClient()

# 向后兼容的别名
aiops_client = local_client 
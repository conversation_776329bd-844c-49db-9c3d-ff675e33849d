2025-05-26 16:07:30 | INFO     | __main__:main:136 - 🔧 AI运维代理 v1.0.0
2025-05-26 16:07:30 | INFO     | __main__:main:137 - ==========================================
2025-05-26 16:07:30 | INFO     | __main__:lifespan:45 - 🚀 AI运维代理启动中...
2025-05-26 16:07:30 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-05-26 16:07:30 | INFO     | __main__:test_model_connections:83 - 🔍 测试模型连接...
2025-05-26 16:07:33 | INFO     | __main__:test_model_connections:91 - ✅ OpenAI连接正常
2025-05-26 16:07:37 | INFO     | __main__:test_model_connections:103 - ✅ vLLM连接正常
2025-05-26 16:07:37 | INFO     | ssh_client:load_service_info:152 - 开始加载服务信息文件: service_info.txt
2025-05-26 16:07:37 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: dify
2025-05-26 16:07:37 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: ragflow
2025-05-26 16:07:37 | INFO     | ssh_client:load_service_info:199 - 服务信息加载完成，共2个服务: ['dify', 'ragflow']
2025-05-26 16:07:37 | INFO     | __main__:load_initial_services:114 - 📋 已加载 2 个服务配置
2025-05-26 16:07:37 | INFO     | __main__:load_initial_services:117 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-05-26 16:07:37 | INFO     | __main__:load_initial_services:117 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-05-26 16:07:37 | INFO     | __main__:lifespan:61 - 🎉 AI运维代理启动完成
2025-05-26 16:07:37 | INFO     | __main__:lifespan:62 - 📡 API服务地址: http://0.0.0.0:8080
2025-05-26 16:07:37 | INFO     | __main__:lifespan:63 - 📋 可用的API端点:
2025-05-26 16:07:37 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-05-26 16:07:37 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-05-26 16:07:37 | INFO     | __main__:lifespan:66 -    GET  /services - 获取服务列表
2025-05-26 16:07:37 | INFO     | __main__:lifespan:67 -    GET  /history - 获取诊断历史
2025-05-26 16:07:37 | INFO     | __main__:lifespan:68 -    POST /manual-command - 手动执行命令
2025-05-26 16:08:36 | INFO     | __main__:lifespan:73 - 🛑 AI运维代理关闭中...
2025-05-26 16:08:36 | INFO     | __main__:lifespan:77 - ✅ SSH连接已清理
2025-05-26 16:08:36 | INFO     | __main__:lifespan:79 - 👋 AI运维代理已关闭
2025-05-26 16:08:41 | INFO     | __main__:main:136 - 🔧 AI运维代理 v1.0.0
2025-05-26 16:08:41 | INFO     | __main__:main:137 - ==========================================
2025-05-26 16:08:41 | INFO     | __main__:lifespan:45 - 🚀 AI运维代理启动中...
2025-05-26 16:08:41 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-05-26 16:08:41 | INFO     | __main__:test_model_connections:83 - 🔍 测试模型连接...
2025-05-26 16:08:43 | INFO     | __main__:test_model_connections:91 - ✅ OpenAI连接正常
2025-05-26 16:08:45 | INFO     | __main__:test_model_connections:103 - ✅ vLLM连接正常
2025-05-26 16:08:45 | INFO     | ssh_client:load_service_info:152 - 开始加载服务信息文件: service_info.txt
2025-05-26 16:08:45 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: dify
2025-05-26 16:08:45 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: ragflow
2025-05-26 16:08:45 | INFO     | ssh_client:load_service_info:199 - 服务信息加载完成，共2个服务: ['dify', 'ragflow']
2025-05-26 16:08:45 | INFO     | __main__:load_initial_services:114 - 📋 已加载 2 个服务配置
2025-05-26 16:08:45 | INFO     | __main__:load_initial_services:117 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-05-26 16:08:45 | INFO     | __main__:load_initial_services:117 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-05-26 16:08:45 | INFO     | __main__:lifespan:61 - 🎉 AI运维代理启动完成
2025-05-26 16:08:45 | INFO     | __main__:lifespan:62 - 📡 API服务地址: http://0.0.0.0:8080
2025-05-26 16:08:45 | INFO     | __main__:lifespan:63 - 📋 可用的API端点:
2025-05-26 16:08:45 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-05-26 16:08:45 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-05-26 16:08:45 | INFO     | __main__:lifespan:66 -    GET  /services - 获取服务列表
2025-05-26 16:08:45 | INFO     | __main__:lifespan:67 -    GET  /history - 获取诊断历史
2025-05-26 16:08:45 | INFO     | __main__:lifespan:68 -    POST /manual-command - 手动执行命令
2025-05-26 16:08:56 | INFO     | api_server:start_continuous_diagnosis:220 - 启动持续诊断: ragflow@***********
2025-05-26 16:08:56 | INFO     | diagnostic_agent:start_continuous_diagnosis:85 - 启动持续诊断会话: ragflow_***********_20250526_160856
2025-05-26 16:08:56 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:98 - 开始持续诊断循环: ragflow_***********_20250526_160856
2025-05-26 16:08:56 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:105 - 会话 ragflow_***********_20250526_160856 - 第 1 次尝试
2025-05-26 16:08:56 | INFO     | ssh_client:get_service_info:210 - 查找服务 'ragflow', 可用服务: ['dify', 'ragflow']
2025-05-26 16:08:56 | INFO     | ssh_client:get_service_info:214 - 找到服务 'ragflow': {'service_name': 'ragflow', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ragflow-main/docker/'}
2025-05-26 16:08:56 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose ps ragflow
2025-05-26 16:08:57 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose logs --tail=50 ragflow
2025-05-26 16:09:04 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/
2025-05-26 16:09:04 | INFO     | diagnostic_agent:_execute_fix_commands:529 - 执行修复命令: docker-compose stop
2025-05-26 16:09:04 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo cd /home/<USER>/workspace/ragflow-main/docker/ && docker-compose stop
2025-05-26 16:09:04 | INFO     | diagnostic_agent:_execute_fix_commands:561 - 命令执行结果: False
2025-05-26 16:09:04 | WARNING  | diagnostic_agent:_execute_fix_commands:565 - 关键Docker命令失败，停止后续执行
2025-05-26 16:09:34 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:105 - 会话 ragflow_***********_20250526_160856 - 第 2 次尝试
2025-05-26 16:09:34 | INFO     | ssh_client:get_service_info:210 - 查找服务 'ragflow', 可用服务: ['dify', 'ragflow']
2025-05-26 16:09:34 | INFO     | ssh_client:get_service_info:214 - 找到服务 'ragflow': {'service_name': 'ragflow', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ragflow-main/docker/'}
2025-05-26 16:09:34 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose ps ragflow
2025-05-26 16:09:34 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose logs --tail=50 ragflow
2025-05-26 16:09:42 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/
2025-05-26 16:09:42 | INFO     | diagnostic_agent:_execute_fix_commands:529 - 执行修复命令: docker-compose ps
2025-05-26 16:09:42 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo cd /home/<USER>/workspace/ragflow-main/docker/ && docker-compose ps
2025-05-26 16:09:42 | INFO     | diagnostic_agent:_execute_fix_commands:561 - 命令执行结果: False
2025-05-26 16:09:42 | WARNING  | diagnostic_agent:_execute_fix_commands:565 - 关键Docker命令失败，停止后续执行
2025-05-26 16:10:12 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:105 - 会话 ragflow_***********_20250526_160856 - 第 3 次尝试
2025-05-26 16:10:12 | INFO     | ssh_client:get_service_info:210 - 查找服务 'ragflow', 可用服务: ['dify', 'ragflow']
2025-05-26 16:10:12 | INFO     | ssh_client:get_service_info:214 - 找到服务 'ragflow': {'service_name': 'ragflow', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ragflow-main/docker/'}
2025-05-26 16:10:12 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose ps ragflow
2025-05-26 16:10:12 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose logs --tail=50 ragflow
2025-05-26 16:10:21 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/
2025-05-26 16:10:21 | INFO     | diagnostic_agent:_execute_fix_commands:529 - 执行修复命令: docker-compose down
2025-05-26 16:10:21 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo cd /home/<USER>/workspace/ragflow-main/docker/ && docker-compose down
2025-05-26 16:10:21 | INFO     | diagnostic_agent:_execute_fix_commands:561 - 命令执行结果: False
2025-05-26 16:10:21 | WARNING  | diagnostic_agent:_execute_fix_commands:565 - 关键Docker命令失败，停止后续执行
2025-05-26 16:10:24 | INFO     | __main__:lifespan:73 - 🛑 AI运维代理关闭中...
2025-05-26 16:10:24 | INFO     | __main__:lifespan:77 - ✅ SSH连接已清理
2025-05-26 16:10:24 | INFO     | __main__:lifespan:79 - 👋 AI运维代理已关闭
2025-05-26 16:27:55 | INFO     | __main__:main:136 - 🔧 AI运维代理 v1.0.0
2025-05-26 16:27:55 | INFO     | __main__:main:137 - ==========================================
2025-05-26 16:27:55 | INFO     | __main__:lifespan:45 - 🚀 AI运维代理启动中...
2025-05-26 16:27:55 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-05-26 16:27:55 | INFO     | __main__:test_model_connections:83 - 🔍 测试模型连接...
2025-05-26 16:27:56 | INFO     | __main__:test_model_connections:91 - ✅ OpenAI连接正常
2025-05-26 16:27:59 | INFO     | __main__:test_model_connections:103 - ✅ vLLM连接正常
2025-05-26 16:27:59 | INFO     | ssh_client:load_service_info:152 - 开始加载服务信息文件: service_info.txt
2025-05-26 16:27:59 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: dify
2025-05-26 16:27:59 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: ragflow
2025-05-26 16:27:59 | INFO     | ssh_client:load_service_info:199 - 服务信息加载完成，共2个服务: ['dify', 'ragflow']
2025-05-26 16:27:59 | INFO     | __main__:load_initial_services:114 - 📋 已加载 2 个服务配置
2025-05-26 16:27:59 | INFO     | __main__:load_initial_services:117 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-05-26 16:27:59 | INFO     | __main__:load_initial_services:117 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-05-26 16:27:59 | INFO     | __main__:lifespan:61 - 🎉 AI运维代理启动完成
2025-05-26 16:27:59 | INFO     | __main__:lifespan:62 - 📡 API服务地址: http://0.0.0.0:8080
2025-05-26 16:27:59 | INFO     | __main__:lifespan:63 - 📋 可用的API端点:
2025-05-26 16:27:59 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-05-26 16:27:59 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-05-26 16:27:59 | INFO     | __main__:lifespan:66 -    GET  /services - 获取服务列表
2025-05-26 16:27:59 | INFO     | __main__:lifespan:67 -    GET  /history - 获取诊断历史
2025-05-26 16:27:59 | INFO     | __main__:lifespan:68 -    POST /manual-command - 手动执行命令
2025-05-26 16:28:03 | INFO     | api_server:start_continuous_diagnosis:220 - 启动持续诊断: ragflow@***********
2025-05-26 16:28:03 | INFO     | diagnostic_agent:start_continuous_diagnosis:85 - 启动持续诊断会话: ragflow_***********_20250526_162803
2025-05-26 16:28:03 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:98 - 开始持续诊断循环: ragflow_***********_20250526_162803
2025-05-26 16:28:03 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:105 - 会话 ragflow_***********_20250526_162803 - 第 1 次尝试
2025-05-26 16:28:03 | INFO     | ssh_client:get_service_info:210 - 查找服务 'ragflow', 可用服务: ['dify', 'ragflow']
2025-05-26 16:28:03 | INFO     | ssh_client:get_service_info:214 - 找到服务 'ragflow': {'service_name': 'ragflow', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ragflow-main/docker/'}
2025-05-26 16:28:03 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose ps ragflow
2025-05-26 16:28:04 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose logs --tail=50 ragflow
2025-05-26 16:28:12 | INFO     | diagnostic_agent:_execute_fix_commands:524 - 执行修复命令: docker-compose stop
2025-05-26 16:28:12 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo bash -c "cd /home/<USER>/workspace/ragflow-main/docker/ && docker-compose stop"
2025-05-26 16:28:24 | INFO     | diagnostic_agent:_execute_fix_commands:560 - 命令执行结果: True
2025-05-26 16:28:26 | INFO     | diagnostic_agent:_execute_fix_commands:524 - 执行修复命令: docker-compose start
2025-05-26 16:28:26 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo bash -c "cd /home/<USER>/workspace/ragflow-main/docker/ && docker-compose start"
2025-05-26 16:28:38 | INFO     | diagnostic_agent:_execute_fix_commands:560 - 命令执行结果: True
2025-05-26 16:28:50 | INFO     | ssh_client:get_service_info:210 - 查找服务 'ragflow', 可用服务: ['dify', 'ragflow']
2025-05-26 16:28:50 | INFO     | ssh_client:get_service_info:214 - 找到服务 'ragflow': {'service_name': 'ragflow', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ragflow-main/docker/'}
2025-05-26 16:28:50 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose ps ragflow
2025-05-26 16:28:51 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose logs --tail=10 ragflow
2025-05-26 16:28:51 | WARNING  | diagnostic_agent:_continuous_diagnosis_loop:121 - 会话 ragflow_***********_20250526_162803 - 命令执行成功但服务仍有问题，继续诊断
2025-05-26 16:29:21 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:105 - 会话 ragflow_***********_20250526_162803 - 第 2 次尝试
2025-05-26 16:29:21 | INFO     | ssh_client:get_service_info:210 - 查找服务 'ragflow', 可用服务: ['dify', 'ragflow']
2025-05-26 16:29:21 | INFO     | ssh_client:get_service_info:214 - 找到服务 'ragflow': {'service_name': 'ragflow', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ragflow-main/docker/'}
2025-05-26 16:29:21 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose ps ragflow
2025-05-26 16:29:21 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose logs --tail=50 ragflow
2025-05-26 16:29:27 | INFO     | diagnostic_agent:_execute_fix_commands:524 - 执行修复命令: docker-compose ps
2025-05-26 16:29:27 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo bash -c "cd /home/<USER>/workspace/ragflow-main/docker/ && docker-compose ps"
2025-05-26 16:29:27 | INFO     | diagnostic_agent:_execute_fix_commands:560 - 命令执行结果: True
2025-05-26 16:29:29 | INFO     | diagnostic_agent:_execute_fix_commands:524 - 执行修复命令: docker images
2025-05-26 16:29:29 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo docker images
2025-05-26 16:29:29 | INFO     | diagnostic_agent:_execute_fix_commands:560 - 命令执行结果: True
2025-05-26 16:29:31 | INFO     | diagnostic_agent:_execute_fix_commands:524 - 执行修复命令: df -h
2025-05-26 16:29:31 | INFO     | ssh_client:execute_command:55 - 执行命令: bash -c "cd /home/<USER>/workspace/ragflow-main/docker/ && df -h"
2025-05-26 16:29:31 | INFO     | diagnostic_agent:_execute_fix_commands:560 - 命令执行结果: True
2025-05-26 16:29:33 | INFO     | diagnostic_agent:_execute_fix_commands:524 - 执行修复命令: free -h
2025-05-26 16:29:33 | INFO     | ssh_client:execute_command:55 - 执行命令: bash -c "cd /home/<USER>/workspace/ragflow-main/docker/ && free -h"
2025-05-26 16:29:33 | INFO     | diagnostic_agent:_execute_fix_commands:560 - 命令执行结果: True
2025-05-26 16:29:45 | INFO     | ssh_client:get_service_info:210 - 查找服务 'ragflow', 可用服务: ['dify', 'ragflow']
2025-05-26 16:29:45 | INFO     | ssh_client:get_service_info:214 - 找到服务 'ragflow': {'service_name': 'ragflow', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ragflow-main/docker/'}
2025-05-26 16:29:45 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose ps ragflow
2025-05-26 16:29:45 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose logs --tail=10 ragflow
2025-05-26 16:29:45 | WARNING  | diagnostic_agent:_continuous_diagnosis_loop:121 - 会话 ragflow_***********_20250526_162803 - 命令执行成功但服务仍有问题，继续诊断
2025-05-26 16:30:15 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:105 - 会话 ragflow_***********_20250526_162803 - 第 3 次尝试
2025-05-26 16:30:15 | INFO     | ssh_client:get_service_info:210 - 查找服务 'ragflow', 可用服务: ['dify', 'ragflow']
2025-05-26 16:30:15 | INFO     | ssh_client:get_service_info:214 - 找到服务 'ragflow': {'service_name': 'ragflow', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ragflow-main/docker/'}
2025-05-26 16:30:15 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose ps ragflow
2025-05-26 16:30:15 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose logs --tail=50 ragflow
2025-05-26 16:30:25 | INFO     | diagnostic_agent:_execute_fix_commands:524 - 执行修复命令: docker-compose down
2025-05-26 16:30:25 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo bash -c "cd /home/<USER>/workspace/ragflow-main/docker/ && docker-compose down"
2025-05-26 16:30:36 | INFO     | diagnostic_agent:_execute_fix_commands:560 - 命令执行结果: True
2025-05-26 16:30:38 | INFO     | diagnostic_agent:_execute_fix_commands:524 - 执行修复命令: docker system prune -f
2025-05-26 16:30:38 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo docker system prune -f
2025-05-26 16:30:38 | INFO     | diagnostic_agent:_execute_fix_commands:560 - 命令执行结果: True
2025-05-26 16:30:40 | INFO     | diagnostic_agent:_execute_fix_commands:524 - 执行修复命令: docker-compose up -d
2025-05-26 16:30:40 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo bash -c "cd /home/<USER>/workspace/ragflow-main/docker/ && docker-compose up -d"
2025-05-26 16:30:52 | INFO     | diagnostic_agent:_execute_fix_commands:560 - 命令执行结果: True
2025-05-26 16:31:04 | INFO     | ssh_client:get_service_info:210 - 查找服务 'ragflow', 可用服务: ['dify', 'ragflow']
2025-05-26 16:31:04 | INFO     | ssh_client:get_service_info:214 - 找到服务 'ragflow': {'service_name': 'ragflow', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ragflow-main/docker/'}
2025-05-26 16:31:04 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose ps ragflow
2025-05-26 16:31:04 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ragflow-main/docker/ && sudo docker-compose logs --tail=10 ragflow
2025-05-26 16:31:04 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:118 - 会话 ragflow_***********_20250526_162803 - 问题已解决！
2025-05-26 16:31:04 | INFO     | diagnostic_agent:_generate_final_report:411 - 最终报告生成完成: ragflow_***********_20250526_162803
2025-05-26 16:31:04 | INFO     | diagnostic_agent:_generate_final_report:412 - 报告摘要: 问题已在第 3 次尝试中解决
2025-05-26 16:31:04 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:153 - 会话 ragflow_***********_20250526_162803 结束，状态: resolved
2025-05-27 09:42:20 | INFO     | __main__:main:136 - 🔧 AI运维代理 v1.0.0
2025-05-27 09:42:20 | INFO     | __main__:main:137 - ==========================================
2025-05-27 09:42:21 | INFO     | __main__:lifespan:45 - 🚀 AI运维代理启动中...
2025-05-27 09:42:21 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-05-27 09:42:21 | INFO     | __main__:test_model_connections:83 - 🔍 测试模型连接...
2025-05-27 09:42:22 | INFO     | __main__:test_model_connections:91 - ✅ OpenAI连接正常
2025-05-27 09:42:25 | INFO     | __main__:test_model_connections:103 - ✅ vLLM连接正常
2025-05-27 09:42:25 | INFO     | ssh_client:load_service_info:152 - 开始加载服务信息文件: service_info.txt
2025-05-27 09:42:25 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: dify
2025-05-27 09:42:25 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: ragflow
2025-05-27 09:42:25 | INFO     | ssh_client:load_service_info:199 - 服务信息加载完成，共2个服务: ['dify', 'ragflow']
2025-05-27 09:42:25 | INFO     | __main__:load_initial_services:114 - 📋 已加载 2 个服务配置
2025-05-27 09:42:25 | INFO     | __main__:load_initial_services:117 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-05-27 09:42:25 | INFO     | __main__:load_initial_services:117 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-05-27 09:42:25 | INFO     | __main__:lifespan:61 - 🎉 AI运维代理启动完成
2025-05-27 09:42:25 | INFO     | __main__:lifespan:62 - 📡 API服务地址: http://0.0.0.0:8080
2025-05-27 09:42:25 | INFO     | __main__:lifespan:63 - 📋 可用的API端点:
2025-05-27 09:42:25 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-05-27 09:42:25 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-05-27 09:42:25 | INFO     | __main__:lifespan:66 -    GET  /services - 获取服务列表
2025-05-27 09:42:25 | INFO     | __main__:lifespan:67 -    GET  /history - 获取诊断历史
2025-05-27 09:42:25 | INFO     | __main__:lifespan:68 -    POST /manual-command - 手动执行命令
2025-05-28 09:18:16 | INFO     | __main__:main:136 - 🔧 AI运维代理 v1.0.0
2025-05-28 09:18:16 | INFO     | __main__:main:137 - ==========================================
2025-05-28 09:18:16 | INFO     | __main__:lifespan:45 - 🚀 AI运维代理启动中...
2025-05-28 09:18:16 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-05-28 09:18:16 | INFO     | __main__:test_model_connections:83 - 🔍 测试模型连接...
2025-05-28 09:18:16 | ERROR    | model_client:chat_completion:156 - vLLM API调用失败: Server error '502 Bad Gateway' for url 'http://10.10.18.210:8288/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-05-28 09:18:17 | WARNING  | __main__:test_model_connections:93 - ⚠️ OpenAI连接可能有问题
2025-05-28 09:18:17 | ERROR    | model_client:chat_completion:156 - vLLM API调用失败: Server error '502 Bad Gateway' for url 'http://10.10.18.210:8288/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-05-28 09:18:17 | WARNING  | __main__:test_model_connections:105 - ⚠️ vLLM连接可能有问题
2025-05-28 09:18:17 | INFO     | ssh_client:load_service_info:152 - 开始加载服务信息文件: service_info.txt
2025-05-28 09:18:17 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: dify
2025-05-28 09:18:17 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: ragflow
2025-05-28 09:18:17 | INFO     | ssh_client:load_service_info:199 - 服务信息加载完成，共2个服务: ['dify', 'ragflow']
2025-05-28 09:18:17 | INFO     | __main__:load_initial_services:114 - 📋 已加载 2 个服务配置
2025-05-28 09:18:17 | INFO     | __main__:load_initial_services:117 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-05-28 09:18:17 | INFO     | __main__:load_initial_services:117 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-05-28 09:18:17 | INFO     | __main__:lifespan:61 - 🎉 AI运维代理启动完成
2025-05-28 09:18:17 | INFO     | __main__:lifespan:62 - 📡 API服务地址: http://0.0.0.0:8080
2025-05-28 09:18:17 | INFO     | __main__:lifespan:63 - 📋 可用的API端点:
2025-05-28 09:18:17 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-05-28 09:18:17 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-05-28 09:18:17 | INFO     | __main__:lifespan:66 -    GET  /services - 获取服务列表
2025-05-28 09:18:17 | INFO     | __main__:lifespan:67 -    GET  /history - 获取诊断历史
2025-05-28 09:18:17 | INFO     | __main__:lifespan:68 -    POST /manual-command - 手动执行命令
2025-05-28 09:24:55 | INFO     | __main__:main:138 - AI运维代理 v1.0.0
2025-05-28 09:24:55 | INFO     | __main__:main:139 - ==========================================
2025-05-28 09:24:55 | INFO     | __main__:lifespan:45 - AI运维代理启动中...
2025-05-28 09:24:55 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-05-28 09:24:55 | INFO     | __main__:test_model_connections:83 - 测试模型连接...
2025-05-28 09:24:55 | WARNING  | __main__:test_model_connections:109 - ⚠️ vLLM连接失败: 'AIOpsModelClient' object has no attribute 'vllm_client'
2025-05-28 09:24:55 | INFO     | ssh_client:load_service_info:152 - 开始加载服务信息文件: service_info.txt
2025-05-28 09:24:55 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: dify
2025-05-28 09:24:55 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: ragflow
2025-05-28 09:24:55 | INFO     | ssh_client:load_service_info:199 - 服务信息加载完成，共2个服务: ['dify', 'ragflow']
2025-05-28 09:24:55 | INFO     | __main__:load_initial_services:116 - 已加载 2 个服务配置
2025-05-28 09:24:55 | INFO     | __main__:load_initial_services:119 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-05-28 09:24:55 | INFO     | __main__:load_initial_services:119 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-05-28 09:24:55 | INFO     | __main__:lifespan:61 - AI运维代理启动完成
2025-05-28 09:24:55 | INFO     | __main__:lifespan:62 - API服务地址: http://0.0.0.0:8080
2025-05-28 09:24:55 | INFO     | __main__:lifespan:63 - 可用的API端点:
2025-05-28 09:24:55 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-05-28 09:24:55 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-05-28 09:24:55 | INFO     | __main__:lifespan:66 -    GET  /services - 获取服务列表
2025-05-28 09:24:55 | INFO     | __main__:lifespan:67 -    GET  /history - 获取诊断历史
2025-05-28 09:24:55 | INFO     | __main__:lifespan:68 -    POST /manual-command - 手动执行命令
2025-05-28 09:24:55 | INFO     | __main__:lifespan:73 - AI运维代理关闭中...
2025-05-28 09:24:55 | INFO     | __main__:lifespan:77 - ✅ SSH连接已清理
2025-05-28 09:24:55 | INFO     | __main__:lifespan:79 - AI运维代理已关闭
2025-05-28 09:26:12 | INFO     | __main__:main:138 - AI运维代理 v1.0.0
2025-05-28 09:26:12 | INFO     | __main__:main:139 - ==========================================
2025-05-28 09:26:12 | INFO     | __main__:lifespan:45 - AI运维代理启动中...
2025-05-28 09:26:12 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-05-28 09:26:12 | INFO     | __main__:test_model_connections:83 - 测试模型连接...
2025-05-28 09:26:12 | ERROR    | model_client:chat_completion:156 - vLLM API调用失败: Server error '502 Bad Gateway' for url 'http://10.10.18.210:8288/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-05-28 09:26:12 | WARNING  | __main__:test_model_connections:107 - ⚠️ vLLM连接可能有问题
2025-05-28 09:26:12 | INFO     | ssh_client:load_service_info:152 - 开始加载服务信息文件: service_info.txt
2025-05-28 09:26:12 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: dify
2025-05-28 09:26:12 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: ragflow
2025-05-28 09:26:12 | INFO     | ssh_client:load_service_info:199 - 服务信息加载完成，共2个服务: ['dify', 'ragflow']
2025-05-28 09:26:12 | INFO     | __main__:load_initial_services:116 - 已加载 2 个服务配置
2025-05-28 09:26:12 | INFO     | __main__:load_initial_services:119 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-05-28 09:26:12 | INFO     | __main__:load_initial_services:119 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-05-28 09:26:12 | INFO     | __main__:lifespan:61 - AI运维代理启动完成
2025-05-28 09:26:12 | INFO     | __main__:lifespan:62 - API服务地址: http://0.0.0.0:8080
2025-05-28 09:26:12 | INFO     | __main__:lifespan:63 - 可用的API端点:
2025-05-28 09:26:12 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-05-28 09:26:12 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-05-28 09:26:12 | INFO     | __main__:lifespan:66 -    GET  /services - 获取服务列表
2025-05-28 09:26:12 | INFO     | __main__:lifespan:67 -    GET  /history - 获取诊断历史
2025-05-28 09:26:12 | INFO     | __main__:lifespan:68 -    POST /manual-command - 手动执行命令
2025-05-28 09:26:12 | INFO     | __main__:lifespan:73 - AI运维代理关闭中...
2025-05-28 09:26:12 | INFO     | __main__:lifespan:77 - ✅ SSH连接已清理
2025-05-28 09:26:12 | INFO     | __main__:lifespan:79 - AI运维代理已关闭
2025-05-28 09:26:49 | INFO     | __main__:main:138 - AI运维代理 v1.0.0
2025-05-28 09:26:49 | INFO     | __main__:main:139 - ==========================================
2025-05-28 09:26:49 | INFO     | __main__:lifespan:45 - AI运维代理启动中...
2025-05-28 09:26:49 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-05-28 09:26:49 | INFO     | __main__:test_model_connections:83 - 测试模型连接...
2025-05-28 09:26:49 | ERROR    | model_client:chat_completion:156 - vLLM API调用失败: Server error '502 Bad Gateway' for url 'http://10.10.18.210:8288/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-05-28 09:26:49 | WARNING  | __main__:test_model_connections:107 - ⚠️ vLLM连接可能有问题
2025-05-28 09:26:49 | INFO     | ssh_client:load_service_info:152 - 开始加载服务信息文件: service_info.txt
2025-05-28 09:26:49 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: dify
2025-05-28 09:26:49 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: ragflow
2025-05-28 09:26:49 | INFO     | ssh_client:load_service_info:199 - 服务信息加载完成，共2个服务: ['dify', 'ragflow']
2025-05-28 09:26:49 | INFO     | __main__:load_initial_services:116 - 已加载 2 个服务配置
2025-05-28 09:26:49 | INFO     | __main__:load_initial_services:119 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-05-28 09:26:49 | INFO     | __main__:load_initial_services:119 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-05-28 09:26:49 | INFO     | __main__:lifespan:61 - AI运维代理启动完成
2025-05-28 09:26:49 | INFO     | __main__:lifespan:62 - API服务地址: http://0.0.0.0:8080
2025-05-28 09:26:49 | INFO     | __main__:lifespan:63 - 可用的API端点:
2025-05-28 09:26:49 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-05-28 09:26:49 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-05-28 09:26:49 | INFO     | __main__:lifespan:66 -    GET  /services - 获取服务列表
2025-05-28 09:26:49 | INFO     | __main__:lifespan:67 -    GET  /history - 获取诊断历史
2025-05-28 09:26:49 | INFO     | __main__:lifespan:68 -    POST /manual-command - 手动执行命令
2025-05-28 14:00:33 | INFO     | __main__:main:72 - AI运维代理集成服务器 v1.0.0
2025-05-28 14:00:33 | INFO     | __main__:main:73 - ==========================================
2025-05-28 14:00:33 | INFO     | __main__:main:89 - 集成服务器启动完成
2025-05-28 14:00:33 | INFO     | __main__:main:90 - API服务: http://0.0.0.0:8080/api
2025-05-28 14:00:33 | INFO     | __main__:main:91 - MCP服务: http://0.0.0.0:8080/mcp
2025-05-28 14:00:33 | INFO     | __main__:main:92 - SSE端点: http://0.0.0.0:8080/mcp/sse
2025-05-29 10:36:30 | INFO     | __main__:main:138 - AI运维代理 v1.0.0
2025-05-29 10:36:30 | INFO     | __main__:main:139 - ==========================================
2025-05-29 10:36:30 | INFO     | __main__:lifespan:45 - AI运维代理启动中...
2025-05-29 10:36:30 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-05-29 10:36:30 | INFO     | __main__:test_model_connections:83 - 测试模型连接...
2025-05-29 10:36:30 | ERROR    | model_client:chat_completion:156 - vLLM API调用失败: Server error '502 Bad Gateway' for url 'http://10.10.18.210:8288/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-05-29 10:36:30 | WARNING  | __main__:test_model_connections:107 - ⚠️ vLLM连接可能有问题
2025-05-29 10:36:30 | INFO     | ssh_client:load_service_info:152 - 开始加载服务信息文件: service_info.txt
2025-05-29 10:36:30 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: dify
2025-05-29 10:36:30 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: ragflow
2025-05-29 10:36:30 | INFO     | ssh_client:load_service_info:199 - 服务信息加载完成，共2个服务: ['dify', 'ragflow']
2025-05-29 10:36:30 | INFO     | __main__:load_initial_services:116 - 已加载 2 个服务配置
2025-05-29 10:36:30 | INFO     | __main__:load_initial_services:119 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-05-29 10:36:30 | INFO     | __main__:load_initial_services:119 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-05-29 10:36:30 | INFO     | __main__:lifespan:61 - AI运维代理启动完成
2025-05-29 10:36:30 | INFO     | __main__:lifespan:62 - API服务地址: http://0.0.0.0:8080
2025-05-29 10:36:30 | INFO     | __main__:lifespan:63 - 可用的API端点:
2025-05-29 10:36:30 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-05-29 10:36:30 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-05-29 10:36:30 | INFO     | __main__:lifespan:66 -    GET  /services - 获取服务列表
2025-05-29 10:36:30 | INFO     | __main__:lifespan:67 -    GET  /history - 获取诊断历史
2025-05-29 10:36:30 | INFO     | __main__:lifespan:68 -    POST /manual-command - 手动执行命令
2025-06-06 14:47:37 | INFO     | __main__:main:138 - AI运维代理 v1.0.0
2025-06-06 14:47:37 | INFO     | __main__:main:139 - ==========================================
2025-06-06 14:47:37 | INFO     | __main__:lifespan:45 - AI运维代理启动中...
2025-06-06 14:47:37 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-06-06 14:47:37 | INFO     | __main__:test_model_connections:83 - 测试模型连接...
2025-06-06 14:47:37 | INFO     | __main__:test_model_connections:105 - ✅ vLLM连接正常
2025-06-06 14:47:37 | INFO     | ssh_client:load_service_info:152 - 开始加载服务信息文件: service_info.txt
2025-06-06 14:47:37 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: dify
2025-06-06 14:47:37 | INFO     | ssh_client:load_service_info:184 - 成功解析服务: ragflow
2025-06-06 14:47:37 | INFO     | ssh_client:load_service_info:199 - 服务信息加载完成，共2个服务: ['dify', 'ragflow']
2025-06-06 14:47:37 | INFO     | __main__:load_initial_services:116 - 已加载 2 个服务配置
2025-06-06 14:47:37 | INFO     | __main__:load_initial_services:119 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-06-06 14:47:37 | INFO     | __main__:load_initial_services:119 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-06-06 14:47:37 | INFO     | __main__:lifespan:61 - AI运维代理启动完成
2025-06-06 14:47:37 | INFO     | __main__:lifespan:62 - API服务地址: http://0.0.0.0:8080
2025-06-06 14:47:37 | INFO     | __main__:lifespan:63 - 可用的API端点:
2025-06-06 14:47:37 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-06-06 14:47:37 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-06-06 14:47:37 | INFO     | __main__:lifespan:66 -    GET  /services - 获取服务列表
2025-06-06 14:47:37 | INFO     | __main__:lifespan:67 -    GET  /history - 获取诊断历史
2025-06-06 14:47:37 | INFO     | __main__:lifespan:68 -    POST /manual-command - 手动执行命令
2025-06-11 10:37:44 | INFO     | __main__:main:126 - AI运维代理 v1.0.0
2025-06-11 10:37:44 | INFO     | __main__:main:127 - ==========================================
2025-06-11 10:37:44 | INFO     | __main__:lifespan:45 - AI运维代理启动中...
2025-06-11 10:37:44 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-06-11 10:37:44 | INFO     | __main__:test_model_connections:85 - 测试本地模型连接...
2025-06-11 10:37:45 | ERROR    | model_client:chat_completion:37 - 本地模型API调用失败: Server error '502 Bad Gateway' for url 'http://10.10.18.210:8288/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-06-11 10:37:45 | WARNING  | __main__:test_model_connections:94 - ⚠️ 本地模型连接可能有问题
2025-06-11 10:37:45 | INFO     | ssh_client:load_service_info:256 - 开始加载服务信息文件: service_info.txt
2025-06-11 10:37:45 | INFO     | ssh_client:load_service_info:298 - 成功解析服务: dify (类型: docker-compose)
2025-06-11 10:37:45 | INFO     | ssh_client:load_service_info:298 - 成功解析服务: ragflow (类型: docker-compose)
2025-06-11 10:37:45 | INFO     | ssh_client:load_service_info:298 - 成功解析服务: ocr_1111_test (类型: supervisor)
2025-06-11 10:37:45 | INFO     | ssh_client:load_service_info:316 - 服务信息加载完成，共3个服务: ['dify', 'ragflow', 'ocr_1111_test']
2025-06-11 10:37:45 | INFO     | __main__:load_initial_services:104 - 已加载 3 个服务配置
2025-06-11 10:37:45 | INFO     | __main__:load_initial_services:107 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-06-11 10:37:45 | INFO     | __main__:load_initial_services:107 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-06-11 10:37:45 | INFO     | __main__:load_initial_services:107 -    - ocr_1111_test: /home/<USER>/workspace/ocr_0205
2025-06-11 10:37:45 | INFO     | __main__:lifespan:61 - AI运维代理启动完成
2025-06-11 10:37:45 | INFO     | __main__:lifespan:62 - API服务地址: http://0.0.0.0:8080
2025-06-11 10:37:45 | INFO     | __main__:lifespan:63 - 可用的API端点:
2025-06-11 10:37:45 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-06-11 10:37:45 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-06-11 10:37:45 | INFO     | __main__:lifespan:66 -    POST /service/operation - 统一服务操作 (docker-compose/supervisor)
2025-06-11 10:37:45 | INFO     | __main__:lifespan:67 -    GET  /service/{service_name}/info - 获取服务配置信息
2025-06-11 10:37:45 | INFO     | __main__:lifespan:68 -    GET  /services - 获取服务列表
2025-06-11 10:37:45 | INFO     | __main__:lifespan:69 -    GET  /history - 获取诊断历史
2025-06-11 10:37:45 | INFO     | __main__:lifespan:70 -    POST /manual-command - 手动执行命令
2025-06-11 10:46:25 | INFO     | __main__:lifespan:75 - AI运维代理关闭中...
2025-06-11 10:46:25 | INFO     | __main__:lifespan:79 - ✅ SSH连接已清理
2025-06-11 10:46:25 | INFO     | __main__:lifespan:81 - AI运维代理已关闭
2025-06-11 10:46:28 | INFO     | __main__:main:126 - AI运维代理 v1.0.0
2025-06-11 10:46:28 | INFO     | __main__:main:127 - ==========================================
2025-06-11 10:46:28 | INFO     | __main__:lifespan:45 - AI运维代理启动中...
2025-06-11 10:46:28 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-06-11 10:46:28 | INFO     | __main__:test_model_connections:85 - 测试本地模型连接...
2025-06-11 10:46:29 | INFO     | __main__:test_model_connections:92 - ✅ 本地模型连接正常
2025-06-11 10:46:29 | INFO     | ssh_client:load_service_info:256 - 开始加载服务信息文件: service_info.txt
2025-06-11 10:46:29 | INFO     | ssh_client:load_service_info:298 - 成功解析服务: dify (类型: docker-compose)
2025-06-11 10:46:29 | INFO     | ssh_client:load_service_info:298 - 成功解析服务: ragflow (类型: docker-compose)
2025-06-11 10:46:29 | INFO     | ssh_client:load_service_info:298 - 成功解析服务: ocr_1111_test (类型: supervisor)
2025-06-11 10:46:29 | INFO     | ssh_client:load_service_info:316 - 服务信息加载完成，共3个服务: ['dify', 'ragflow', 'ocr_1111_test']
2025-06-11 10:46:29 | INFO     | __main__:load_initial_services:104 - 已加载 3 个服务配置
2025-06-11 10:46:29 | INFO     | __main__:load_initial_services:107 -    - dify: /home/<USER>/workspace/dify-main/docker/
2025-06-11 10:46:29 | INFO     | __main__:load_initial_services:107 -    - ragflow: /home/<USER>/workspace/ragflow-main/docker/
2025-06-11 10:46:29 | INFO     | __main__:load_initial_services:107 -    - ocr_1111_test: /home/<USER>/workspace/ocr_0205
2025-06-11 10:46:29 | INFO     | __main__:lifespan:61 - AI运维代理启动完成
2025-06-11 10:46:29 | INFO     | __main__:lifespan:62 - API服务地址: http://0.0.0.0:8080
2025-06-11 10:46:29 | INFO     | __main__:lifespan:63 - 可用的API端点:
2025-06-11 10:46:29 | INFO     | __main__:lifespan:64 -    POST /diagnose - 诊断服务错误
2025-06-11 10:46:29 | INFO     | __main__:lifespan:65 -    POST /health-check - 服务健康检查
2025-06-11 10:46:29 | INFO     | __main__:lifespan:66 -    POST /service/operation - 统一服务操作 (docker-compose/supervisor)
2025-06-11 10:46:29 | INFO     | __main__:lifespan:67 -    GET  /service/{service_name}/info - 获取服务配置信息
2025-06-11 10:46:29 | INFO     | __main__:lifespan:68 -    GET  /services - 获取服务列表
2025-06-11 10:46:29 | INFO     | __main__:lifespan:69 -    GET  /history - 获取诊断历史
2025-06-11 10:46:29 | INFO     | __main__:lifespan:70 -    POST /manual-command - 手动执行命令
2025-06-11 15:52:03 | INFO     | __main__:lifespan:75 - AI运维代理关闭中...
2025-06-11 15:52:03 | INFO     | __main__:lifespan:79 - ✅ SSH连接已清理
2025-06-11 15:52:03 | INFO     | __main__:lifespan:81 - AI运维代理已关闭
2025-06-18 09:58:33 | INFO     | __main__:main:169 - AI运维代理 v1.0.0
2025-06-18 09:58:33 | INFO     | __main__:main:170 - ==========================================
2025-06-18 09:58:33 | INFO     | __main__:lifespan:45 - AI运维代理启动中...
2025-06-18 09:58:33 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-06-18 09:58:33 | INFO     | __main__:test_model_connections:101 - 测试本地模型连接...
2025-06-18 09:58:34 | ERROR    | model_client:chat_completion:168 - 本地模型API调用失败: Server error '502 Bad Gateway' for url 'http://10.10.18.210:8288/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-06-18 09:58:34 | WARNING  | __main__:test_model_connections:110 - ⚠️ 本地模型连接可能有问题
2025-06-18 09:58:34 | INFO     | __main__:test_mcp_connection:117 - 测试微信MCP连接...
2025-06-18 09:58:34 | INFO     | __main__:test_mcp_connection:123 - ✅ 微信MCP服务可用
2025-06-18 09:58:38 | INFO     | ssh_mcp_client:_call_mcp_tool:251 - ✅ SSH MCP工具调用成功: list_services
2025-06-18 09:58:38 | ERROR    | ssh_mcp_client:list_services:153 - 列出服务失败: Extra data: line 1 column 4 (char 3)
2025-06-18 09:58:38 | WARNING  | __main__:load_initial_services:149 - ⚠️ 未能获取服务配置信息
2025-06-18 09:58:38 | INFO     | __main__:load_initial_services:150 - 请检查service_info.txt文件和SSH MCP服务
2025-06-18 09:58:38 | INFO     | __main__:lifespan:64 - AI运维代理启动完成
2025-06-18 09:58:38 | INFO     | __main__:lifespan:65 - API服务地址: http://0.0.0.0:8000
2025-06-18 09:58:38 | INFO     | __main__:lifespan:69 - 🔐 安全配置: LOW 级别
2025-06-18 09:58:38 | INFO     | __main__:lifespan:70 - 🤖 AI自主权: Limited
2025-06-18 09:58:38 | INFO     | __main__:lifespan:72 - ⚠️  高安全模式: AI仅限使用预定义策略
2025-06-18 09:58:38 | INFO     | __main__:lifespan:76 - 可用的API端点:
2025-06-18 09:58:38 | INFO     | __main__:lifespan:77 -    POST /diagnose - 诊断服务错误
2025-06-18 09:58:38 | INFO     | __main__:lifespan:78 -    POST /health-check - 服务健康检查
2025-06-18 09:58:38 | INFO     | __main__:lifespan:79 -    POST /service/operation - 统一服务操作 (docker-compose/supervisor)
2025-06-18 09:58:38 | INFO     | __main__:lifespan:80 -    GET  /service/{service_name}/info - 获取服务配置信息
2025-06-18 09:58:38 | INFO     | __main__:lifespan:81 -    GET  /services - 获取服务列表
2025-06-18 09:58:38 | INFO     | __main__:lifespan:82 -    GET  /history - 获取诊断历史
2025-06-18 09:58:38 | INFO     | __main__:lifespan:83 -    POST /manual-command - 手动执行命令
2025-06-18 09:58:38 | INFO     | __main__:lifespan:84 -    GET  /security/config - 获取安全配置
2025-06-18 09:58:38 | INFO     | __main__:lifespan:85 -    POST /security/config - 更新安全配置 (运行时)
2025-06-18 09:58:38 | INFO     | __main__:lifespan:86 -    POST /wechat/notify - 发送微信MCP通知
2025-06-18 09:58:38 | INFO     | __main__:lifespan:87 -    GET  /wechat/test - 测试微信MCP连接
2025-06-18 11:16:50 | INFO     | __main__:main:213 - AI运维代理 v1.0.0
2025-06-18 11:16:50 | INFO     | __main__:main:214 - ==========================================
2025-06-18 11:16:51 | INFO     | __main__:lifespan:45 - AI运维代理启动中...
2025-06-18 11:16:51 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-06-18 11:16:51 | INFO     | __main__:test_model_connections:112 - 测试本地模型连接...
2025-06-18 11:16:54 | ERROR    | model_client:chat_completion:168 - 本地模型API调用失败: Server error '502 Bad Gateway' for url 'http://10.10.18.210:8288/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-06-18 11:16:54 | WARNING  | __main__:test_model_connections:121 - ⚠️ 本地模型连接可能有问题
2025-06-18 11:16:54 | INFO     | __main__:test_mcp_connection:128 - 测试微信MCP连接...
2025-06-18 11:16:54 | INFO     | __main__:test_mcp_connection:134 - ✅ 微信MCP服务可用
2025-06-18 11:16:57 | INFO     | ssh_mcp_client:_call_mcp_tool:251 - ✅ SSH MCP工具调用成功: list_services
2025-06-18 11:16:57 | ERROR    | ssh_mcp_client:list_services:153 - 列出服务失败: Extra data: line 1 column 4 (char 3)
2025-06-18 11:16:57 | WARNING  | __main__:load_initial_services:160 - ⚠️ 未能获取服务配置信息
2025-06-18 11:16:57 | INFO     | __main__:load_initial_services:161 - 请检查service_info.txt文件和SSH MCP服务
2025-06-18 11:16:57 | INFO     | __main__:start_monitoring_service:169 - 启动监控服务...
2025-06-18 11:16:57 | INFO     | __main__:start_monitoring_service:175 - ✅ 监控服务已加载 1 个服务配置
2025-06-18 11:16:57 | INFO     | __main__:start_monitoring_service:179 - ✅ 监控服务已启动，开始持续监控服务健康状态
2025-06-18 11:16:57 | INFO     | __main__:start_monitoring_service:183 -    📊 监控中: ocr_test (http://***********:1111/check)
2025-06-18 11:16:57 | INFO     | __main__:lifespan:67 - AI运维代理启动完成
2025-06-18 11:16:57 | INFO     | __main__:lifespan:68 - API服务地址: http://0.0.0.0:8000
2025-06-18 11:16:57 | INFO     | __main__:lifespan:72 - 🔐 安全配置: LOW 级别
2025-06-18 11:16:57 | INFO     | __main__:lifespan:73 - 🤖 AI自主权: Limited
2025-06-18 11:16:57 | INFO     | __main__:lifespan:75 - ⚠️  高安全模式: AI仅限使用预定义策略
2025-06-18 11:16:57 | INFO     | __main__:lifespan:79 - 可用的API端点:
2025-06-18 11:16:57 | INFO     | __main__:lifespan:80 -    POST /diagnose - 诊断服务错误
2025-06-18 11:16:57 | INFO     | __main__:lifespan:81 -    POST /health-check - 服务健康检查
2025-06-18 11:16:57 | INFO     | __main__:lifespan:82 -    POST /service/operation - 统一服务操作 (docker-compose/supervisor)
2025-06-18 11:16:57 | INFO     | __main__:lifespan:83 -    GET  /service/{service_name}/info - 获取服务配置信息
2025-06-18 11:16:57 | INFO     | __main__:lifespan:84 -    GET  /services - 获取服务列表
2025-06-18 11:16:57 | INFO     | __main__:lifespan:85 -    GET  /history - 获取诊断历史
2025-06-18 11:16:57 | INFO     | __main__:lifespan:86 -    POST /manual-command - 手动执行命令
2025-06-18 11:16:57 | INFO     | __main__:lifespan:87 -    GET  /security/config - 获取安全配置
2025-06-18 11:16:57 | INFO     | __main__:lifespan:88 -    POST /security/config - 更新安全配置 (运行时)
2025-06-18 11:16:57 | INFO     | __main__:lifespan:89 -    POST /wechat/notify - 发送微信MCP通知
2025-06-18 11:16:57 | INFO     | __main__:lifespan:90 -    GET  /wechat/test - 测试微信MCP连接
2025-06-18 11:16:57 | INFO     | __main__:lifespan:91 -    POST /monitor/add - 添加服务到监控
2025-06-18 11:16:57 | INFO     | __main__:lifespan:92 -    POST /monitor/remove - 从监控移除服务
2025-06-18 11:16:57 | INFO     | __main__:lifespan:93 -    GET  /monitor/status - 获取监控状态
2025-06-18 11:16:57 | INFO     | __main__:lifespan:94 -    POST /monitor/start - 启动监控
2025-06-18 11:16:57 | INFO     | __main__:lifespan:95 -    POST /monitor/stop - 停止监控
2025-06-18 11:19:32 | ERROR    | wechat_mcp_client:_call_mcp_tool:187 - MCP工具调用异常: text must be False
2025-06-18 11:19:33 | INFO     | api_server:start_continuous_diagnosis:236 - 启动持续诊断: nginx@localhost
2025-06-18 11:19:33 | INFO     | diagnostic_agent:start_continuous_diagnosis:87 - 启动持续诊断会话: nginx_localhost_20250618_111933
2025-06-18 11:19:33 | ERROR    | api_server:start_continuous_diagnosis:259 - 启动持续诊断失败: WechatMCPClient.send_diagnosis_update() missing 1 required positional argument: 'message'
2025-06-18 11:19:33 | ERROR    | api_server:general_exception_handler:685 - 未处理的异常: 'APIResponse' object is not callable
2025-06-18 11:19:44 | INFO     | api_server:start_continuous_diagnosis:236 - 启动持续诊断: docker-service@localhost
2025-06-18 11:19:44 | INFO     | diagnostic_agent:start_continuous_diagnosis:87 - 启动持续诊断会话: docker-service_localhost_20250618_111944
2025-06-18 11:19:44 | ERROR    | api_server:start_continuous_diagnosis:259 - 启动持续诊断失败: WechatMCPClient.send_diagnosis_update() missing 1 required positional argument: 'message'
2025-06-18 11:19:44 | ERROR    | api_server:general_exception_handler:685 - 未处理的异常: 'APIResponse' object is not callable
2025-06-18 11:19:56 | INFO     | api_server:start_continuous_diagnosis:236 - 启动持续诊断: test@localhost
2025-06-18 11:19:56 | INFO     | diagnostic_agent:start_continuous_diagnosis:87 - 启动持续诊断会话: test_localhost_20250618_111956
2025-06-18 11:19:56 | ERROR    | api_server:start_continuous_diagnosis:259 - 启动持续诊断失败: WechatMCPClient.send_diagnosis_update() missing 1 required positional argument: 'message'
2025-06-18 11:19:56 | ERROR    | api_server:general_exception_handler:685 - 未处理的异常: 'APIResponse' object is not callable
2025-06-18 11:24:09 | INFO     | __main__:lifespan:100 - AI运维代理关闭中...
2025-06-18 11:24:09 | INFO     | __main__:stop_monitoring_service:193 - 停止监控服务...
2025-06-18 11:29:48 | INFO     | __main__:main:213 - AI运维代理 v1.0.0
2025-06-18 11:29:48 | INFO     | __main__:main:214 - ==========================================
2025-06-18 11:29:49 | INFO     | __main__:lifespan:45 - AI运维代理启动中...
2025-06-18 11:29:49 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-06-18 11:29:49 | INFO     | __main__:test_model_connections:112 - 测试本地模型连接...
2025-06-18 11:29:50 | INFO     | __main__:test_model_connections:119 - ✅ 本地模型连接正常
2025-06-18 11:29:50 | INFO     | __main__:test_mcp_connection:128 - 测试微信MCP连接...
2025-06-18 11:29:50 | INFO     | __main__:test_mcp_connection:134 - ✅ 微信MCP服务可用
2025-06-18 11:29:54 | ERROR    | ssh_mcp_client:_call_mcp_tool:283 - ❌ SSH MCP工具调用失败: D:\Users\i_liuqingyun\AppData\Local\Programs\Python\Python311\Lib\site-packages\paramiko\pkey.py:100: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from cryptography.hazmat.primitives.ciphers.algorithms in 48.0.0.

  "cipher": algorithms.TripleDES,

D:\Users\i_liuqingyun\AppData\Local\Programs\Python\Python311\Lib\site-packages\paramiko\transport.py:258: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from cryptography.hazmat.primitives.ciphers.algorithms in 48.0.0.

  "class": algorithms.TripleDES,

Traceback (most recent call last):

  File "D:\Users\i_liuqingyun\Desktop\Ops_agent\ssh_mcp\main.py", line 14, in <module>

    from mcp.server import Server

ModuleNotFoundError: No module named 'mcp'


2025-06-18 11:29:54 | ERROR    | ssh_mcp_client:_call_mcp_tool:287 - SSH MCP工具调用异常: MCP工具调用失败: D:\Users\i_liuqingyun\AppData\Local\Programs\Python\Python311\Lib\site-packages\paramiko\pkey.py:100: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from cryptography.hazmat.primitives.ciphers.algorithms in 48.0.0.

  "cipher": algorithms.TripleDES,

D:\Users\i_liuqingyun\AppData\Local\Programs\Python\Python311\Lib\site-packages\paramiko\transport.py:258: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from cryptography.hazmat.primitives.ciphers.algorithms in 48.0.0.

  "class": algorithms.TripleDES,

Traceback (most recent call last):

  File "D:\Users\i_liuqingyun\Desktop\Ops_agent\ssh_mcp\main.py", line 14, in <module>

    from mcp.server import Server

ModuleNotFoundError: No module named 'mcp'


2025-06-18 11:29:54 | ERROR    | ssh_mcp_client:list_services:153 - 列出服务失败: MCP工具调用失败: D:\Users\i_liuqingyun\AppData\Local\Programs\Python\Python311\Lib\site-packages\paramiko\pkey.py:100: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from cryptography.hazmat.primitives.ciphers.algorithms in 48.0.0.

  "cipher": algorithms.TripleDES,

D:\Users\i_liuqingyun\AppData\Local\Programs\Python\Python311\Lib\site-packages\paramiko\transport.py:258: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from cryptography.hazmat.primitives.ciphers.algorithms in 48.0.0.

  "class": algorithms.TripleDES,

Traceback (most recent call last):

  File "D:\Users\i_liuqingyun\Desktop\Ops_agent\ssh_mcp\main.py", line 14, in <module>

    from mcp.server import Server

ModuleNotFoundError: No module named 'mcp'


2025-06-18 11:29:54 | WARNING  | __main__:load_initial_services:160 - ⚠️ 未能获取服务配置信息
2025-06-18 11:29:54 | INFO     | __main__:load_initial_services:161 - 请检查service_info.txt文件和SSH MCP服务
2025-06-18 11:29:54 | INFO     | __main__:start_monitoring_service:169 - 启动监控服务...
2025-06-18 11:29:54 | INFO     | __main__:start_monitoring_service:175 - ✅ 监控服务已加载 1 个服务配置
2025-06-18 11:29:54 | INFO     | __main__:start_monitoring_service:179 - ✅ 监控服务已启动，开始持续监控服务健康状态
2025-06-18 11:29:54 | INFO     | __main__:start_monitoring_service:183 -    📊 监控中: ocr_test (http://***********:1111/check)
2025-06-18 11:29:54 | INFO     | __main__:lifespan:67 - AI运维代理启动完成
2025-06-18 11:29:54 | INFO     | __main__:lifespan:68 - API服务地址: http://0.0.0.0:8000
2025-06-18 11:29:54 | INFO     | __main__:lifespan:72 - 🔐 安全配置: LOW 级别
2025-06-18 11:29:54 | INFO     | __main__:lifespan:73 - 🤖 AI自主权: Limited
2025-06-18 11:29:54 | INFO     | __main__:lifespan:75 - ⚠️  高安全模式: AI仅限使用预定义策略
2025-06-18 11:29:54 | INFO     | __main__:lifespan:79 - 可用的API端点:
2025-06-18 11:29:54 | INFO     | __main__:lifespan:80 -    POST /diagnose - 诊断服务错误
2025-06-18 11:29:54 | INFO     | __main__:lifespan:81 -    POST /health-check - 服务健康检查
2025-06-18 11:29:54 | INFO     | __main__:lifespan:82 -    POST /service/operation - 统一服务操作 (docker-compose/supervisor)
2025-06-18 11:29:54 | INFO     | __main__:lifespan:83 -    GET  /service/{service_name}/info - 获取服务配置信息
2025-06-18 11:29:54 | INFO     | __main__:lifespan:84 -    GET  /services - 获取服务列表
2025-06-18 11:29:54 | INFO     | __main__:lifespan:85 -    GET  /history - 获取诊断历史
2025-06-18 11:29:54 | INFO     | __main__:lifespan:86 -    POST /manual-command - 手动执行命令
2025-06-18 11:29:54 | INFO     | __main__:lifespan:87 -    GET  /security/config - 获取安全配置
2025-06-18 11:29:54 | INFO     | __main__:lifespan:88 -    POST /security/config - 更新安全配置 (运行时)
2025-06-18 11:29:54 | INFO     | __main__:lifespan:89 -    POST /wechat/notify - 发送微信MCP通知
2025-06-18 11:29:54 | INFO     | __main__:lifespan:90 -    GET  /wechat/test - 测试微信MCP连接
2025-06-18 11:29:54 | INFO     | __main__:lifespan:91 -    POST /monitor/add - 添加服务到监控
2025-06-18 11:29:54 | INFO     | __main__:lifespan:92 -    POST /monitor/remove - 从监控移除服务
2025-06-18 11:29:54 | INFO     | __main__:lifespan:93 -    GET  /monitor/status - 获取监控状态
2025-06-18 11:29:54 | INFO     | __main__:lifespan:94 -    POST /monitor/start - 启动监控
2025-06-18 11:29:54 | INFO     | __main__:lifespan:95 -    POST /monitor/stop - 停止监控
2025-06-18 11:34:58 | ERROR    | wechat_mcp_client:_call_mcp_tool:187 - MCP工具调用异常: text must be False
2025-06-18 11:34:59 | INFO     | api_server:start_continuous_diagnosis:236 - 启动持续诊断: nginx@localhost
2025-06-18 11:34:59 | INFO     | diagnostic_agent:start_continuous_diagnosis:87 - 启动持续诊断会话: nginx_localhost_20250618_113459
2025-06-18 11:34:59 | ERROR    | api_server:start_continuous_diagnosis:259 - 启动持续诊断失败: WechatMCPClient.send_diagnosis_update() missing 1 required positional argument: 'message'
2025-06-18 11:34:59 | ERROR    | api_server:general_exception_handler:685 - 未处理的异常: 'APIResponse' object is not callable
2025-06-18 11:35:10 | INFO     | api_server:start_continuous_diagnosis:236 - 启动持续诊断: docker-service@localhost
2025-06-18 11:35:10 | INFO     | diagnostic_agent:start_continuous_diagnosis:87 - 启动持续诊断会话: docker-service_localhost_20250618_113510
2025-06-18 11:35:10 | ERROR    | api_server:start_continuous_diagnosis:259 - 启动持续诊断失败: WechatMCPClient.send_diagnosis_update() missing 1 required positional argument: 'message'
2025-06-18 11:35:10 | ERROR    | api_server:general_exception_handler:685 - 未处理的异常: 'APIResponse' object is not callable
2025-06-18 11:37:31 | ERROR    | wechat_mcp_client:_call_mcp_tool:187 - MCP工具调用异常: text must be False
2025-06-18 11:37:32 | INFO     | api_server:start_continuous_diagnosis:236 - 启动持续诊断: nginx@localhost
2025-06-18 11:37:32 | INFO     | diagnostic_agent:start_continuous_diagnosis:87 - 启动持续诊断会话: nginx_localhost_20250618_113732
2025-06-18 11:37:32 | ERROR    | api_server:start_continuous_diagnosis:259 - 启动持续诊断失败: WechatMCPClient.send_diagnosis_update() missing 1 required positional argument: 'message'
2025-06-18 11:37:32 | ERROR    | api_server:general_exception_handler:685 - 未处理的异常: 'APIResponse' object is not callable
2025-06-18 11:37:43 | INFO     | api_server:start_continuous_diagnosis:236 - 启动持续诊断: docker-service@localhost
2025-06-18 11:37:43 | INFO     | diagnostic_agent:start_continuous_diagnosis:87 - 启动持续诊断会话: docker-service_localhost_20250618_113743
2025-06-18 11:37:43 | ERROR    | api_server:start_continuous_diagnosis:259 - 启动持续诊断失败: WechatMCPClient.send_diagnosis_update() missing 1 required positional argument: 'message'
2025-06-18 11:37:43 | ERROR    | api_server:general_exception_handler:685 - 未处理的异常: 'APIResponse' object is not callable
2025-06-18 15:39:08 | INFO     | __main__:main:213 - AI运维代理 v1.0.0
2025-06-18 15:39:08 | INFO     | __main__:main:214 - ==========================================
2025-06-18 15:39:08 | INFO     | __main__:lifespan:45 - AI运维代理启动中...
2025-06-18 15:39:08 | INFO     | __main__:lifespan:50 - ✅ 配置验证通过
2025-06-18 15:39:08 | INFO     | __main__:test_model_connections:112 - 测试本地模型连接...
2025-06-18 15:39:09 | INFO     | __main__:test_model_connections:119 - ✅ 本地模型连接正常
2025-06-18 15:39:09 | INFO     | __main__:test_mcp_connection:128 - 测试微信MCP连接...
2025-06-18 15:39:09 | INFO     | __main__:test_mcp_connection:134 - ✅ 微信MCP服务可用
2025-06-18 15:39:12 | INFO     | ssh_mcp_client:_call_mcp_tool:251 - ✅ SSH MCP工具调用成功: list_services
2025-06-18 15:39:12 | ERROR    | ssh_mcp_client:list_services:153 - 列出服务失败: Extra data: line 1 column 4 (char 3)
2025-06-18 15:39:12 | WARNING  | __main__:load_initial_services:160 - ⚠️ 未能获取服务配置信息
2025-06-18 15:39:12 | INFO     | __main__:load_initial_services:161 - 请检查service_info.txt文件和SSH MCP服务
2025-06-18 15:39:12 | INFO     | __main__:start_monitoring_service:169 - 启动监控服务...
2025-06-18 15:39:12 | INFO     | __main__:start_monitoring_service:175 - ✅ 监控服务已加载 1 个服务配置
2025-06-18 15:39:12 | INFO     | __main__:start_monitoring_service:179 - ✅ 监控服务已启动，开始持续监控服务健康状态
2025-06-18 15:39:12 | INFO     | __main__:start_monitoring_service:183 -    📊 监控中: ocr_test (http://***********:1111/check)
2025-06-18 15:39:12 | INFO     | __main__:lifespan:67 - AI运维代理启动完成
2025-06-18 15:39:12 | INFO     | __main__:lifespan:68 - API服务地址: http://0.0.0.0:8000
2025-06-18 15:39:12 | INFO     | __main__:lifespan:72 - 🔐 安全配置: LOW 级别
2025-06-18 15:39:12 | INFO     | __main__:lifespan:73 - 🤖 AI自主权: Limited
2025-06-18 15:39:12 | INFO     | __main__:lifespan:75 - ⚠️  高安全模式: AI仅限使用预定义策略
2025-06-18 15:39:12 | INFO     | __main__:lifespan:79 - 可用的API端点:
2025-06-18 15:39:12 | INFO     | __main__:lifespan:80 -    POST /diagnose - 诊断服务错误
2025-06-18 15:39:12 | INFO     | __main__:lifespan:81 -    POST /health-check - 服务健康检查
2025-06-18 15:39:12 | INFO     | __main__:lifespan:82 -    POST /service/operation - 统一服务操作 (docker-compose/supervisor)
2025-06-18 15:39:12 | INFO     | __main__:lifespan:83 -    GET  /service/{service_name}/info - 获取服务配置信息
2025-06-18 15:39:12 | INFO     | __main__:lifespan:84 -    GET  /services - 获取服务列表
2025-06-18 15:39:12 | INFO     | __main__:lifespan:85 -    GET  /history - 获取诊断历史
2025-06-18 15:39:12 | INFO     | __main__:lifespan:86 -    POST /manual-command - 手动执行命令
2025-06-18 15:39:12 | INFO     | __main__:lifespan:87 -    GET  /security/config - 获取安全配置
2025-06-18 15:39:12 | INFO     | __main__:lifespan:88 -    POST /security/config - 更新安全配置 (运行时)
2025-06-18 15:39:12 | INFO     | __main__:lifespan:89 -    POST /wechat/notify - 发送微信MCP通知
2025-06-18 15:39:12 | INFO     | __main__:lifespan:90 -    GET  /wechat/test - 测试微信MCP连接
2025-06-18 15:39:12 | INFO     | __main__:lifespan:91 -    POST /monitor/add - 添加服务到监控
2025-06-18 15:39:12 | INFO     | __main__:lifespan:92 -    POST /monitor/remove - 从监控移除服务
2025-06-18 15:39:12 | INFO     | __main__:lifespan:93 -    GET  /monitor/status - 获取监控状态
2025-06-18 15:39:12 | INFO     | __main__:lifespan:94 -    POST /monitor/start - 启动监控
2025-06-18 15:39:12 | INFO     | __main__:lifespan:95 -    POST /monitor/stop - 停止监控
2025-06-18 15:39:12 | INFO     | __main__:lifespan:100 - AI运维代理关闭中...
2025-06-18 15:39:12 | INFO     | __main__:stop_monitoring_service:193 - 停止监控服务...
2025-06-18 15:42:31 | INFO     | __main__:main:213 - AI运维代理 v1.0.0
2025-06-18 15:42:31 | INFO     | __main__:main:214 - ==========================================
2025-06-18 15:42:31 | INFO     | __main__:test_model_connections:112 - 测试本地模型连接...
2025-06-18 15:42:32 | INFO     | __main__:test_model_connections:119 - ✅ 本地模型连接正常
2025-06-18 15:42:32 | INFO     | __main__:test_mcp_connection:128 - 测试微信MCP连接...
2025-06-18 15:42:32 | INFO     | __main__:test_mcp_connection:134 - ✅ 微信MCP服务可用
2025-06-18 15:42:33 | INFO     | ssh_mcp_client:_call_mcp_tool:251 - ✅ SSH MCP工具调用成功: list_services
2025-06-18 15:42:33 | ERROR    | ssh_mcp_client:list_services:153 - 列出服务失败: Extra data: line 1 column 4 (char 3)
2025-06-18 15:42:33 | WARNING  | __main__:load_initial_services:160 - ⚠️ 未能获取服务配置信息
2025-06-18 15:42:33 | INFO     | __main__:load_initial_services:161 - 请检查service_info.txt文件和SSH MCP服务
2025-06-18 15:42:33 | INFO     | __main__:start_monitoring_service:169 - 启动监控服务...
2025-06-18 15:42:33 | INFO     | __main__:start_monitoring_service:175 - ✅ 监控服务已加载 1 个服务配置
2025-06-18 15:42:33 | INFO     | __main__:start_monitoring_service:179 - ✅ 监控服务已启动，开始持续监控服务健康状态
2025-06-18 15:42:33 | INFO     | __main__:start_monitoring_service:183 -    📊 监控中: ocr_test (http://***********:1111/check)

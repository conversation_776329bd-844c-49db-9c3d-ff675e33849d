#!/usr/bin/env python3
"""
微信MCP客户端
用于与wechat_mcp服务通信，发送企业微信通知
"""

import json
import subprocess
import asyncio
from typing import Optional, Dict, Any, List
from loguru import logger
from pathlib import Path
import os

class WechatMCPClient:
    """微信MCP客户端"""
    
    def __init__(self, mcp_path: str = "wechat_mcp"):
        self.mcp_path = Path(mcp_path)
        self.enabled = self._check_mcp_available()
        
    def _check_mcp_available(self) -> bool:
        """检查MCP服务是否可用"""
        try:
            if not self.mcp_path.exists():
                logger.warning(f"❌ MCP路径不存在: {self.mcp_path}")
                return False
            
            # 检查main.py是否存在
            main_py = self.mcp_path / "main.py"
            if not main_py.exists():
                logger.warning(f"❌ MCP主文件不存在: {main_py}")
                return False
            
            # 检查uv是否可用
            result = subprocess.run(["uv", "--version"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                logger.warning("❌ uv 包管理器不可用")
                return False
            
            logger.info("✅ MCP服务可用")
            return True
            
        except Exception as e:
            logger.warning(f"❌ MCP检查失败: {e}")
            return False
    
    async def send_text_message(self, content: str, mentioned_list: Optional[List[str]] = None) -> bool:
        """发送文本消息"""
        if not self.enabled:
            logger.warning("MCP服务不可用，跳过微信通知")
            return False
        
        try:
            # 构造MCP调用命令
            tool_call = {
                "name": "send_text_message",
                "arguments": {
                    "content": content,
                    "mentioned_list": mentioned_list or []
                }
            }
            
            return await self._call_mcp_tool(tool_call)
            
        except Exception as e:
            logger.error(f"发送文本消息失败: {e}")
            return False
    
    async def send_markdown_message(self, content: str) -> bool:
        """发送Markdown消息"""
        if not self.enabled:
            logger.warning("MCP服务不可用，跳过微信通知")
            return False
        
        try:
            tool_call = {
                "name": "send_markdown_message",
                "arguments": {
                    "content": content
                }
            }
            
            return await self._call_mcp_tool(tool_call)
            
        except Exception as e:
            logger.error(f"发送Markdown消息失败: {e}")
            return False
    
    async def send_system_alert(self, alert_type: str, service_name: str, 
                              server_info: str, description: str, 
                              severity: str = "medium") -> bool:
        """发送系统警报"""
        if not self.enabled:
            logger.warning("MCP服务不可用，跳过微信通知")
            return False
        
        try:
            tool_call = {
                "name": "send_system_alert",
                "arguments": {
                    "alert_type": alert_type,
                    "service_name": service_name,
                    "server_info": server_info,
                    "description": description,
                    "severity": severity
                }
            }
            
            return await self._call_mcp_tool(tool_call)
            
        except Exception as e:
            logger.error(f"发送系统警报失败: {e}")
            return False
    
    async def send_diagnosis_update(self, session_id: str, service_name: str,
                                  server_info: str, status: str, 
                                  message: str, attempts: int = 1) -> bool:
        """发送诊断进度更新"""
        if not self.enabled:
            logger.warning("MCP服务不可用，跳过微信通知")
            return False
        
        try:
            tool_call = {
                "name": "send_diagnosis_update",
                "arguments": {
                    "session_id": session_id,
                    "service_name": service_name,
                    "server_info": server_info,
                    "status": status,
                    "message": message,
                    "attempts": attempts
                }
            }
            
            return await self._call_mcp_tool(tool_call)
            
        except Exception as e:
            logger.error(f"发送诊断更新失败: {e}")
            return False
    
    async def _call_mcp_tool(self, tool_call: Dict[str, Any]) -> bool:
        """调用MCP工具"""
        try:
            # 切换到MCP目录
            original_cwd = os.getcwd()
            os.chdir(self.mcp_path)
            
            # 构造调用命令
            cmd = ["uv", "run", "python", "main.py"]
            
            # 将工具调用作为输入传递
            input_data = json.dumps({
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": tool_call["name"],
                    "arguments": tool_call["arguments"]
                }
            })
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                text=True
            )
            
            stdout, stderr = await process.communicate(input=input_data)
            
            # 恢复原目录
            os.chdir(original_cwd)
            
            if process.returncode == 0:
                logger.info(f"✅ MCP工具调用成功: {tool_call['name']}")
                return True
            else:
                logger.error(f"❌ MCP工具调用失败: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"MCP工具调用异常: {e}")
            # 确保恢复原目录
            try:
                os.chdir(original_cwd)
            except:
                pass
            return False
    
    async def test_connection(self) -> bool:
        """测试MCP连接"""
        if not self.enabled:
            return False
        
        try:
            # 发送一条测试消息
            test_message = "🧪 AI运维代理MCP连接测试"
            return await self.send_text_message(test_message)
            
        except Exception as e:
            logger.error(f"MCP连接测试失败: {e}")
            return False

# 全局MCP客户端实例
wechat_mcp_client = WechatMCPClient()

# 向后兼容的别名
wechat_mcp = wechat_mcp_client 
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from loguru import logger
import asyncio
from datetime import datetime

from diagnostic_agent import diagnostic_agent
from ssh_mcp_client import ssh_mcp_client
from wechat_mcp_client import wechat_mcp_client
from monitor_service import MonitorService

# API数据模型
class ErrorReport(BaseModel):
    """错误报告模型"""
    service_name: str
    server_ip: str
    error_info: str

class HealthCheckRequest(BaseModel):
    """健康检查请求模型"""
    service_name: str
    server_ip: str

class ManualCommand(BaseModel):
    """手动命令执行模型"""
    service_name: str
    server_ip: str
    commands: List[str]
    use_sudo: Optional[bool] = True

class ContinuousDiagnosisRequest(BaseModel):
    """持续诊断请求模型"""
    service_name: str
    server_ip: str
    error_info: str
    max_attempts: Optional[int] = 10

class SessionControlRequest(BaseModel):
    """会话控制请求模型"""
    session_id: str
    
class WechatNotificationRequest(BaseModel):
    """企业微信通知请求模型"""
    message_type: str  # text, markdown
    content: str
    mentioned_list: Optional[List[str]] = None

class ServiceMonitorInfo(BaseModel):
    """服务监控信息模型"""
    service_name: str
    url: str
    server_ip: str

class ServiceOperationRequest(BaseModel):
    """服务操作请求模型"""
    service_name: str
    operation: str  # start, stop, restart, status, logs
    lines: Optional[int] = 100  # 用于日志操作

class APIResponse(BaseModel):
    """标准API响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: str

# 创建FastAPI应用
app = FastAPI(
    title="AI运维代理",
    description="智能服务运维代理API - 支持Docker和Supervisor服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建监控服务实例
monitor_service = MonitorService(diagnostic_agent=diagnostic_agent)

# 启动时加载服务配置
monitor_service.load_services_from_config()

@app.get("/", response_model=APIResponse)
async def root():
    """根路径，API状态检查"""
    return APIResponse(
        success=True,
        message="AI运维代理API运行正常",
        timestamp=datetime.now().isoformat()
    )

@app.post("/diagnose", response_model=APIResponse)
async def diagnose_service_error(error_report: ErrorReport):
    """
    诊断服务错误并自动修复
    
    这是主要的错误处理接口，接收包含服务名称和服务器IP的错误信息
    """
    try:
        logger.info(f"收到错误报告: {error_report.service_name}@{error_report.server_ip}")
        
        # 自动启动持续诊断会话（所有请求都使用持续诊断）
        session_id = await diagnostic_agent.start_continuous_diagnosis(
            error_report.service_name,
            error_report.server_ip,
            error_report.error_info,
            max_attempts=10  # 默认最多尝试10次
        )
        
        return APIResponse(
            success=True,
            message="已启动持续诊断会话，将自动修复直到问题解决或方案用尽",
            data={
                "session_id": session_id,
                "service_name": error_report.service_name,
                "server_ip": error_report.server_ip,
                "status": "持续诊断中",
                "message": "使用 GET /session/{session_id} 查看进度"
            },
            timestamp=datetime.now().isoformat()
        )
            
    except Exception as e:
        logger.error(f"诊断接口错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"诊断过程失败: {str(e)}"
        )

@app.post("/health-check", response_model=APIResponse)
async def health_check_service(health_request: HealthCheckRequest):
    """
    服务健康检查
    """
    try:
        result = await diagnostic_agent.get_service_health_check(
            health_request.service_name,
            health_request.server_ip
        )
        
        return APIResponse(
            success=result.get("success", False),
            message="健康检查完成",
            data=result,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"健康检查错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"健康检查失败: {str(e)}"
        )

@app.get("/services", response_model=APIResponse)
async def list_services():
    """
    获取所有可用服务列表
    """
    try:
        # 使用SSH MCP客户端获取服务列表
        services_result = await ssh_mcp_client.list_services()
        
        if services_result.get("success", False) and "services" in services_result:
            services = services_result["services"]
            
            service_list = []
            for service in services:
                service_list.append({
                    "service_name": service.get("name"),
                    "hostname": service.get("hostname"),
                    "directory": service.get("directory"),
                    "service_type": service.get("service_type"),
                    # 不返回密码信息
                })
            
            return APIResponse(
                success=True,
                message=f"找到 {len(service_list)} 个服务",
                data={"services": service_list},
                timestamp=datetime.now().isoformat()
            )
        else:
            return APIResponse(
                success=False,
                message="获取服务列表失败",
                data={"error": services_result.get("error", "未知错误")},
                timestamp=datetime.now().isoformat()
            )
        
    except Exception as e:
        logger.error(f"获取服务列表错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取服务列表失败: {str(e)}"
        )

@app.get("/history", response_model=APIResponse)
async def get_diagnostic_history(limit: int = 10):
    """
    获取诊断历史记录
    """
    try:
        history = diagnostic_agent.get_diagnostic_history(limit)
        
        return APIResponse(
            success=True,
            message=f"获取到 {len(history)} 条历史记录",
            data={"history": history},
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"获取历史记录错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取历史记录失败: {str(e)}"
        )

@app.post("/continuous-diagnosis", response_model=APIResponse)
async def start_continuous_diagnosis(diagnosis_request: ContinuousDiagnosisRequest):
    """
    启动持续诊断会话 - 新的主要诊断接口
    AI代理将持续尝试直到问题解决或方案用尽
    """
    try:
        logger.info(f"启动持续诊断: {diagnosis_request.service_name}@{diagnosis_request.server_ip}")
        
        session_id = await diagnostic_agent.start_continuous_diagnosis(
            diagnosis_request.service_name,
            diagnosis_request.server_ip,
            diagnosis_request.error_info,
            diagnosis_request.max_attempts
        )
        
        return APIResponse(
            success=True,
            message="持续诊断会话已启动",
            data={
                "session_id": session_id,
                "service_name": diagnosis_request.service_name,
                "server_ip": diagnosis_request.server_ip,
                "max_attempts": diagnosis_request.max_attempts,
                "status": "启动中"
            },
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"启动持续诊断失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"启动持续诊断失败: {str(e)}"
        )

@app.get("/session/{session_id}", response_model=APIResponse)
async def get_session_status(session_id: str):
    """
    获取诊断会话状态
    """
    try:
        status = diagnostic_agent.get_session_status(session_id)
        
        if not status:
            raise HTTPException(
                status_code=404,
                detail=f"会话 {session_id} 不存在"
            )
        
        return APIResponse(
            success=True,
            message="会话状态获取成功",
            data=status,
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取会话状态失败: {str(e)}"
        )

@app.post("/session/stop", response_model=APIResponse)
async def stop_diagnosis_session(session_request: SessionControlRequest):
    """
    停止诊断会话
    """
    try:
        success = diagnostic_agent.stop_session(session_request.session_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail=f"会话 {session_request.session_id} 不存在或已结束"
            )
        
        return APIResponse(
            success=True,
            message=f"会话 {session_request.session_id} 已停止",
            data={"session_id": session_request.session_id},
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止会话失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"停止会话失败: {str(e)}"
        )

@app.get("/sessions", response_model=APIResponse)
async def list_active_sessions():
    """
    列出所有活跃的诊断会话
    """
    try:
        sessions = diagnostic_agent.list_active_sessions()
        
        return APIResponse(
            success=True,
            message=f"找到 {len(sessions)} 个活跃会话",
            data={"active_sessions": sessions},
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"获取活跃会话失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取活跃会话失败: {str(e)}"
        )

@app.post("/manual-command", response_model=APIResponse)
async def execute_manual_command(manual_cmd: ManualCommand):
    """
    手动执行命令
    
    提供手动执行命令的接口，用于紧急修复或调试
    """
    try:
        # 获取服务列表以查找目标服务
        services_result = await ssh_mcp_client.list_services()
        
        service_info = None
        if services_result.get("success", False) and "services" in services_result:
            for service in services_result["services"]:
                if service["name"] == manual_cmd.service_name:
                    service_info = service
                    break
        
        if not service_info:
            raise HTTPException(
                status_code=404,
                detail=f"服务 '{manual_cmd.service_name}' 未找到"
            )
        
        results = []
        
        # 执行每个命令
        for command in manual_cmd.commands:
            try:
                # 使用SSH MCP客户端执行远程命令
                result = await ssh_mcp_client.execute_remote_command(
                    hostname=manual_cmd.server_ip,
                    username="ubuntu",  # 从配置中获取，这里暂时硬编码
                    password="tf$Ke^HB5lm&",  # 从配置中获取，这里暂时硬编码
                    command=f"cd {service_info['directory']} && {command}",
                    use_sudo=manual_cmd.use_sudo,
                    timeout=60
                )
                
                results.append({
                    "command": command,
                    "success": result.get("success", False),
                    "exit_status": result.get("exit_status", -1),
                    "stdout": result.get("stdout", ""),
                    "stderr": result.get("stderr", ""),
                    "error": result.get("error", "") if not result.get("success", False) else None
                })
                
            except Exception as cmd_error:
                logger.error(f"执行命令失败: {command}, 错误: {cmd_error}")
                results.append({
                    "command": command,
                    "success": False,
                    "error": str(cmd_error)
                })
        
        # 统计执行结果
        success_count = sum(1 for r in results if r.get("success", False))
        total_commands = len(results)
        
        return APIResponse(
            success=success_count > 0,
            message=f"执行完成: {success_count}/{total_commands} 命令成功",
            data={
                "service_name": manual_cmd.service_name,
                "server_ip": manual_cmd.server_ip,
                "results": results,
                "summary": {
                    "total_commands": total_commands,
                    "successful": success_count,
                    "failed": total_commands - success_count
                }
            },
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"手动命令执行错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"命令执行失败: {str(e)}"
        )

@app.get("/status/{service_name}/{server_ip}", response_model=APIResponse)
async def get_service_status(service_name: str, server_ip: str):
    """
    获取特定服务的当前状态
    """
    try:
        result = await diagnostic_agent.get_service_health_check(service_name, server_ip)
        
        return APIResponse(
            success=result.get("success", False),
            message="状态查询完成",
            data=result,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"状态查询错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"状态查询失败: {str(e)}"
        )

@app.post("/service/operation", response_model=APIResponse)
async def execute_service_operation(operation_request: ServiceOperationRequest):
    """
    统一服务操作接口 (支持Docker Compose和Supervisor)
    
    支持的操作:
    - start: 启动服务
    - stop: 停止服务  
    - restart: 重启服务
    - status: 获取服务状态
    - logs: 获取服务日志
    """
    try:
        # 直接使用SSH MCP客户端的服务操作功能
        if operation_request.operation.lower() == "logs":
            result = await ssh_mcp_client.get_service_logs(
                operation_request.service_name, 
                lines=operation_request.lines
            )
        elif operation_request.operation.lower() == "status":
            result = await ssh_mcp_client.get_service_status(
                operation_request.service_name
            )
        else:
            # 对于其他操作（start, stop, restart）
            result = await ssh_mcp_client.execute_service_operation(
                operation_request.service_name,
                operation_request.operation,
                log_lines=operation_request.lines
            )
        
        return APIResponse(
            success=result.get("success", False),
            message=f"服务操作 '{operation_request.operation}' 完成",
            data={
                "service_name": operation_request.service_name,
                "operation": operation_request.operation,
                "result": result
            },
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"服务操作错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"服务操作失败: {str(e)}"
        )

@app.get("/service/{service_name}/info", response_model=APIResponse)
async def get_service_info_endpoint(service_name: str):
    """
    获取服务配置信息
    """
    try:
        # 获取服务列表以查找目标服务
        services_result = await ssh_mcp_client.list_services()
        
        service_info = None
        if services_result.get("success", False) and "services" in services_result:
            for service in services_result["services"]:
                if service["name"] == service_name:
                    service_info = service
                    break
        
        if not service_info:
            raise HTTPException(
                status_code=404,
                detail=f"服务 '{service_name}' 未找到"
            )
        
        # 隐藏敏感信息（密码）
        safe_info = {k: v for k, v in service_info.items() if k != "password"}
        
        return APIResponse(
            success=True,
            message=f"服务 {service_name} 信息获取成功",
            data={
                "service_name": service_name,
                "service_info": safe_info
            },
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取服务信息错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取服务信息失败: {str(e)}"
        )

# 异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    return APIResponse(
        success=False,
        message=exc.detail,
        timestamp=datetime.now().isoformat()
    )

@app.post("/wechat/notify", response_model=APIResponse)
async def send_wechat_notification(notification: WechatNotificationRequest):
    """发送企业微信MCP通知"""
    try:
        success = False
        
        if notification.message_type == "text":
            success = await wechat_mcp_client.send_text_message(
                notification.content,
                notification.mentioned_list
            )
        elif notification.message_type == "markdown":
            success = await wechat_mcp_client.send_markdown_message(notification.content)
        else:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的消息类型: {notification.message_type}"
            )
        
        return APIResponse(
            success=success,
            message="企业微信MCP通知已发送" if success else "企业微信MCP通知发送失败",
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"发送企业微信MCP通知失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"发送企业微信MCP通知失败: {str(e)}"
        )

@app.get("/wechat/test", response_model=APIResponse)
async def test_wechat_notification():
    """测试企业微信MCP通知"""
    try:
        # 使用MCP发送测试消息
        success = await wechat_mcp_client.test_connection()
        
        return APIResponse(
            success=success,
            message="企业微信MCP测试通知已发送" if success else "企业微信MCP测试通知发送失败",
            data={
                "mcp_enabled": wechat_mcp_client.enabled,
                "mcp_path": str(wechat_mcp_client.mcp_path)
            },
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"测试企业微信MCP通知失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"测试企业微信MCP通知失败: {str(e)}"
        )

@app.get("/security/config", response_model=APIResponse)
async def get_security_config():
    """获取当前安全配置信息"""
    try:
        security_info = config.get_security_info()
        return APIResponse(
            success=True,
            message="安全配置获取成功",
            data={
                "security_config": security_info,
                "description": {
                    "security_level": "安全级别 - high: 高安全(限制AI), low: 低安全(AI自主)",
                    "ai_autonomy": "AI自主权限状态",
                    "is_high_security": "是否为高安全模式",
                    "use_predefined_strategies": "是否使用预定义策略"
                }
            },
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"获取安全配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取安全配置失败: {str(e)}")

class SecurityConfigUpdate(BaseModel):
    """安全配置更新模型"""
    security_level: Optional[str] = None  # "high" or "low"
    enable_predefined_strategies: Optional[bool] = None

@app.post("/security/config", response_model=APIResponse)
async def update_security_config(security_data: SecurityConfigUpdate):
    """更新安全配置（仅用于运行时修改，不会持久化到配置文件）"""
    try:
        updated_fields = []
        
        if security_data.security_level is not None:
            new_level = security_data.security_level.lower()
            if new_level not in ["high", "low"]:
                raise HTTPException(status_code=400, detail="安全级别必须是 'high' 或 'low'")
            
            # 运行时修改配置
            config.SECURITY_LEVEL = new_level
            updated_fields.append(f"安全级别: {new_level}")
            logger.info(f"🔐 安全级别已更新为: {new_level}")
            
        if security_data.enable_predefined_strategies is not None:
            config.ENABLE_PREDEFINED_STRATEGIES = security_data.enable_predefined_strategies
            updated_fields.append(f"预设策略: {'启用' if security_data.enable_predefined_strategies else '禁用'}")
            logger.info(f"🎯 预设策略开关已更新为: {security_data.enable_predefined_strategies}")
        
        if not updated_fields:
            raise HTTPException(status_code=400, detail="没有提供要更新的配置项")
        
        updated_info = config.get_security_info()
        logger.warning("⚠️ 安全配置已在运行时修改，重启后将恢复到配置文件中的设置")
        
        return APIResponse(
            success=True,
            message=f"安全配置已更新: {', '.join(updated_fields)} (运行时生效，重启后恢复)",
            data={
                "updated_config": updated_info,
                "updated_fields": updated_fields,
                "note": "此修改仅在运行时生效，重启后将恢复到配置文件设置"
            },
            timestamp=datetime.now().isoformat()
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新安全配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新安全配置失败: {str(e)}")

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return APIResponse(
        success=False,
        message="内部服务器错误",
        timestamp=datetime.now().isoformat()
    )

@app.post("/monitor/add", response_model=APIResponse)
async def add_service_to_monitor(service_info: ServiceMonitorInfo):
    """添加服务到监控列表"""
    try:
        monitor_service.add_service(
            service_name=service_info.service_name,
            url=service_info.url,
            server_ip=service_info.server_ip
        )
        return APIResponse(
            success=True,
            message=f"服务 {service_info.service_name} 已添加到监控列表",
            data={"service_name": service_info.service_name},
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"添加监控服务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"添加监控服务失败: {str(e)}"
        )

@app.post("/monitor/remove", response_model=APIResponse)
async def remove_service_from_monitor(service_info: ServiceMonitorInfo):
    """从监控列表移除服务"""
    try:
        monitor_service.remove_service(service_info.service_name)
        return APIResponse(
            success=True,
            message=f"服务 {service_info.service_name} 已从监控列表移除",
            data={"service_name": service_info.service_name},
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"移除监控服务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"移除监控服务失败: {str(e)}"
        )

@app.get("/monitor/status", response_model=APIResponse)
async def get_monitor_status():
    """获取所有监控服务的状态"""
    try:
        status = monitor_service.get_all_services_status()
        return APIResponse(
            success=True,
            message="获取监控状态成功",
            data={"services": status},
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"获取监控状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取监控状态失败: {str(e)}"
        )

@app.post("/monitor/start", response_model=APIResponse)
async def start_monitoring():
    """启动所有服务的监控"""
    try:
        monitor_service.start_monitoring()
        return APIResponse(
            success=True,
            message="监控服务已启动",
            data={"status": "running"},
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"启动监控服务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"启动监控服务失败: {str(e)}"
        )

@app.post("/monitor/stop", response_model=APIResponse)
async def stop_monitoring():
    """停止所有服务的监控"""
    try:
        monitor_service.stop_monitoring()
        return APIResponse(
            success=True,
            message="监控服务已停止",
            data={"status": "stopped"},
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"停止监控服务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"停止监控服务失败: {str(e)}"
        )

 
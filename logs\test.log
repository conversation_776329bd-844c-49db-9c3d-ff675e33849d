2025-06-11 10:39:21 | INFO     | __main__:<module>:189 - 开始测试更新后的诊断代理...
2025-06-11 10:39:21 | INFO     | diagnostic_agent:start_continuous_diagnosis:86 - 启动持续诊断会话: ocr_1111_test_10.83.160.7_20250611_103921
2025-06-11 10:39:22 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:104 - 开始持续诊断循环: ocr_1111_test_10.83.160.7_20250611_103921
2025-06-11 10:39:22 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:111 - 会话 ocr_1111_test_10.83.160.7_20250611_103921 - 第 1 次尝试
2025-06-11 10:39:22 | INFO     | ssh_client:load_service_info:256 - 开始加载服务信息文件: service_info.txt
2025-06-11 10:39:22 | DEBUG    | ssh_client:load_service_info:266 - 解析第1行: dify,ubuntu,ubuntu,tf$Ke^HB5lm&,/home/<USER>/workspace/dify-main/docker/,docker-compose,
2025-06-11 10:39:22 | INFO     | ssh_client:load_service_info:298 - 成功解析服务: dify (类型: docker-compose)
2025-06-11 10:39:22 | DEBUG    | ssh_client:load_service_info:266 - 解析第2行: ragflow,ubuntu,ubuntu,tf$Ke^HB5lm&,/home/<USER>/workspace/ragflow-main/docker/,docker-compose,
2025-06-11 10:39:22 | INFO     | ssh_client:load_service_info:298 - 成功解析服务: ragflow (类型: docker-compose)
2025-06-11 10:39:22 | DEBUG    | ssh_client:load_service_info:266 - 解析第3行: ocr_1111_test,ubuntu,ubuntu,tf$Ke^HB5lm&,/home/<USER>/workspace/ocr_0205,supervisor,/etc/supervisor/conf.d/ocr_5106_for_ops_test.conf
2025-06-11 10:39:22 | INFO     | ssh_client:load_service_info:298 - 成功解析服务: ocr_1111_test (类型: supervisor)
2025-06-11 10:39:22 | INFO     | ssh_client:load_service_info:316 - 服务信息加载完成，共3个服务: ['dify', 'ragflow', 'ocr_1111_test']
2025-06-11 10:39:22 | INFO     | ssh_client:get_service_info:327 - 查找服务 'ocr_1111_test', 可用服务: ['dify', 'ragflow', 'ocr_1111_test']
2025-06-11 10:39:22 | INFO     | ssh_client:get_service_info:331 - 找到服务 'ocr_1111_test': {'service_name': 'ocr_1111_test', 'hostname': 'ubuntu', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ocr_0205', 'service_type': 'supervisor', 'config_path': '/etc/supervisor/conf.d/ocr_5106_for_ops_test.conf'}
2025-06-11 10:39:22 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ocr_0205 && sudo docker-compose ps ocr_1111_test
2025-06-11 10:39:23 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ocr_0205 && sudo docker-compose logs --tail=50 ocr_1111_test
2025-06-11 10:39:23 | ERROR    | wechat_bot:_send_message:130 - 企业微信通知发送失败: invalid webhook url, hint: [1749609563118373168456237], from ip: ************, more info at https://open.work.weixin.qq.com/devtool/query?e=93000
2025-06-11 10:39:39 | INFO     | diagnostic_agent:_execute_fix_commands:517 - 执行修复命令: sudo bash -c "cd /home/<USER>/workspace/ocr_0205 && docker-compose stop"
2025-06-11 10:39:39 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo bash -c "cd /home/<USER>/workspace/ocr_0205 && docker-compose stop"
2025-06-11 10:39:39 | INFO     | diagnostic_agent:_execute_fix_commands:537 - 命令执行结果: False
2025-06-11 10:39:39 | WARNING  | diagnostic_agent:_execute_fix_commands:541 - 关键Docker命令失败，停止后续执行
2025-06-11 10:40:09 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:111 - 会话 ocr_1111_test_10.83.160.7_20250611_103921 - 第 2 次尝试
2025-06-11 10:40:09 | INFO     | ssh_client:get_service_info:327 - 查找服务 'ocr_1111_test', 可用服务: ['dify', 'ragflow', 'ocr_1111_test']
2025-06-11 10:40:09 | INFO     | ssh_client:get_service_info:331 - 找到服务 'ocr_1111_test': {'service_name': 'ocr_1111_test', 'hostname': 'ubuntu', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ocr_0205', 'service_type': 'supervisor', 'config_path': '/etc/supervisor/conf.d/ocr_5106_for_ops_test.conf'}
2025-06-11 10:40:09 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ocr_0205 && sudo docker-compose ps ocr_1111_test
2025-06-11 10:40:10 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ocr_0205 && sudo docker-compose logs --tail=50 ocr_1111_test
2025-06-11 10:40:25 | INFO     | diagnostic_agent:_execute_fix_commands:517 - 执行修复命令: sudo bash -c "cd /home/<USER>/workspace/ocr_0205 && docker-compose ps"
2025-06-11 10:40:25 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo bash -c "cd /home/<USER>/workspace/ocr_0205 && docker-compose ps"
2025-06-11 10:40:25 | INFO     | diagnostic_agent:_execute_fix_commands:537 - 命令执行结果: False
2025-06-11 10:40:25 | WARNING  | diagnostic_agent:_execute_fix_commands:541 - 关键Docker命令失败，停止后续执行
2025-06-11 10:40:55 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:111 - 会话 ocr_1111_test_10.83.160.7_20250611_103921 - 第 3 次尝试
2025-06-11 10:40:55 | INFO     | ssh_client:get_service_info:327 - 查找服务 'ocr_1111_test', 可用服务: ['dify', 'ragflow', 'ocr_1111_test']
2025-06-11 10:40:55 | INFO     | ssh_client:get_service_info:331 - 找到服务 'ocr_1111_test': {'service_name': 'ocr_1111_test', 'hostname': 'ubuntu', 'username': 'ubuntu', 'password': 'tf$Ke^HB5lm&', 'directory': '/home/<USER>/workspace/ocr_0205', 'service_type': 'supervisor', 'config_path': '/etc/supervisor/conf.d/ocr_5106_for_ops_test.conf'}
2025-06-11 10:40:55 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ocr_0205 && sudo docker-compose ps ocr_1111_test
2025-06-11 10:40:55 | INFO     | ssh_client:execute_command:55 - 执行命令: cd /home/<USER>/workspace/ocr_0205 && sudo docker-compose logs --tail=50 ocr_1111_test
2025-06-11 10:41:11 | INFO     | diagnostic_agent:_execute_fix_commands:517 - 执行修复命令: sudo bash -c "cd /home/<USER>/workspace/ocr_0205 && docker-compose down"
2025-06-11 10:41:11 | INFO     | ssh_client:execute_command:55 - 执行命令: sudo bash -c "cd /home/<USER>/workspace/ocr_0205 && docker-compose down"
2025-06-11 10:41:11 | INFO     | diagnostic_agent:_execute_fix_commands:537 - 命令执行结果: False
2025-06-11 10:41:11 | WARNING  | diagnostic_agent:_execute_fix_commands:541 - 关键Docker命令失败，停止后续执行
2025-06-11 10:41:41 | INFO     | diagnostic_agent:_generate_final_report:428 - 最终报告生成完成: ocr_1111_test_10.83.160.7_20250611_103921
2025-06-11 10:41:41 | INFO     | diagnostic_agent:_generate_final_report:429 - 报告摘要: 已尝试 3 次，所有方案均已用尽
2025-06-11 10:41:41 | INFO     | diagnostic_agent:_continuous_diagnosis_loop:159 - 会话 ocr_1111_test_10.83.160.7_20250611_103921 结束，状态: exhausted
2025-06-11 10:41:41 | ERROR    | wechat_bot:_send_message:130 - 企业微信通知发送失败: invalid webhook url, hint: [1749609701315354096306905], from ip: ************, more info at https://open.work.weixin.qq.com/devtool/query?e=93000
2025-06-11 10:41:42 | INFO     | __main__:<module>:195 - ✅ 测试完成！

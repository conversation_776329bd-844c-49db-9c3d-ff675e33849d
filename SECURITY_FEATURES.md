# 🔐 AI运维代理安全分级功能

## 概述

AI运维代理现在支持安全分级功能，允许用户根据环境需求选择不同的AI操作权限级别，确保在生产环境中的安全性和在开发环境中的灵活性。

## 安全级别

### 🔒 高安全模式 (High Security)
- **适用场景**: 生产环境、关键业务系统
- **AI权限**: 受限制，只能选择预定义的诊断策略
- **配置**: `SECURITY_LEVEL=high`
- **特点**:
  - AI只能从5个预定义策略中选择
  - 不允许创造新的解决方案
  - 所有操作都有明确的安全边界
  - 命令执行受严格限制

#### 预定义策略列表:
1. **基础服务重启** - 适用于服务无响应、进程异常
2. **端口冲突解决** - 适用于端口被占用、绑定失败
3. **配置文件检查** - 适用于启动失败、配置错误
4. **日志分析重启** - 适用于错误日志明确、故障模式已知
5. **系统资源检查** - 适用于性能问题、资源不足

### 🚀 低安全模式 (Low Security)
- **适用场景**: 开发环境、测试环境、故障紧急处理
- **AI权限**: 完全自主，拥有创新决策权
- **配置**: `SECURITY_LEVEL=low`
- **特点**:
  - AI可以设计全新的诊断策略
  - 允许组合不同的技术手段
  - 可以探索非常规的解决方案
  - 基于实际情况自主调整方案

## 配置方式

### 1. 通过配置文件

编辑 `config.env` 文件:

```env
# 安全级别配置
SECURITY_LEVEL=low                    # high=高安全, low=低安全
ENABLE_PREDEFINED_STRATEGIES=true     # 预设策略开关
```

### 2. 通过API动态调整

#### 获取当前安全配置
```bash
curl -X GET "http://localhost:8000/security/config"
```

#### 更新安全配置（运行时）
```bash
# 设置为高安全模式
curl -X POST "http://localhost:8000/security/config" \
  -H "Content-Type: application/json" \
  -d '{
    "security_level": "high",
    "enable_predefined_strategies": true
  }'

# 设置为低安全模式
curl -X POST "http://localhost:8000/security/config" \
  -H "Content-Type: application/json" \
  -d '{
    "security_level": "low",
    "enable_predefined_strategies": false
  }'
```

## AI响应格式差异

### 高安全模式响应格式
```json
{
  "strategy_used": "策略1: 基础服务重启",
  "diagnosis": "基于选定策略的问题分析",
  "solution": "选定策略的解决方案",
  "commands": ["策略对应的命令列表"],
  "reasoning": "选择该策略的理由"
}
```

### 低安全模式响应格式
```json
{
  "diagnosis": "基于历史分析的深度诊断，指出新的问题角度和可能根因",
  "solution": "创新的解决方案，说明与之前不同的解决思路",
  "commands": ["具体的可执行命令，基于服务类型使用正确的命令格式"],
  "reasoning": "详细说明方案选择的理论依据，与历史方案的本质区别，预期效果"
}
```

## 使用建议

### 生产环境推荐配置
```env
SECURITY_LEVEL=high
ENABLE_PREDEFINED_STRATEGIES=true
```
- ✅ 确保AI操作在安全边界内
- ✅ 避免意外的系统修改
- ✅ 满足合规性要求
- ✅ 可预测的故障处理流程

### 开发环境推荐配置
```env
SECURITY_LEVEL=low
ENABLE_PREDEFINED_STRATEGIES=false
```
- ✅ 充分发挥AI的智能诊断能力
- ✅ 快速解决复杂问题
- ✅ 探索新的解决方案
- ✅ 学习和优化诊断策略

## 测试验证

运行安全分级功能测试:
```bash
python test_security_features.py
```

该测试将验证:
- 配置验证功能
- 高安全模式下的AI响应
- 低安全模式下的AI响应
- 模式切换功能

## 注意事项

1. **配置持久性**: 通过API修改的配置仅在运行时生效，重启后恢复到配置文件设置
2. **向后兼容**: 系统保持对现有API的完全兼容
3. **安全边界**: 高安全模式下AI严格遵循预定义策略，不会偏离安全边界
4. **监控日志**: 所有安全级别变更都会记录在系统日志中

## 配置参数详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `SECURITY_LEVEL` | string | "low" | 安全级别: "high"/"low" |
| `ENABLE_PREDEFINED_STRATEGIES` | boolean | true | 是否启用预设策略 |
| `MAX_DIAGNOSIS_ATTEMPTS` | integer | 5 | 最大诊断尝试次数 |
| `ENABLE_DETAILED_DIAGNOSIS_LOG` | boolean | true | 是否启用详细诊断日志 |

## API端点

| 方法 | 端点 | 说明 |
|------|------|------|
| GET | `/security/config` | 获取当前安全配置 |
| POST | `/security/config` | 更新安全配置（运行时） |

## 故障排查

### 常见问题

1. **配置未生效**
   - 检查配置文件路径是否正确
   - 确认重启服务后配置加载
   - 查看启动日志中的配置信息

2. **API调用失败**
   - 确认安全级别值正确（"high"/"low"）
   - 检查请求格式是否为有效JSON
   - 查看服务器日志排查具体错误

3. **AI响应格式不符**
   - 检查当前安全级别设置
   - 确认AI模型服务正常运行
   - 查看诊断日志了解详细信息

通过这套安全分级机制，AI运维代理能够在保证安全性的同时，根据不同环境需求提供相应的智能化服务。 
#!/usr/bin/env python3
"""
SSH MCP客户端
用于与ssh_mcp服务通信，提供SSH操作和服务管理功能
"""

import json
import subprocess
import asyncio
from typing import Optional, Dict, Any, List
from loguru import logger
from pathlib import Path
import os


class SSHMCPClient:
    """SSH MCP客户端"""
    
    def __init__(self, mcp_path: str = "ssh_mcp"):
        self.mcp_path = Path(mcp_path)
        self.enabled = self._check_mcp_available()
        
    def _check_mcp_available(self) -> bool:
        """检查MCP服务是否可用"""
        try:
            if not self.mcp_path.exists():
                logger.warning(f"❌ SSH MCP路径不存在: {self.mcp_path}")
                return False
            
            # 检查main.py是否存在
            main_py = self.mcp_path / "main.py"
            if not main_py.exists():
                logger.warning(f"❌ SSH MCP主文件不存在: {main_py}")
                return False
            
            logger.info("✅ SSH MCP服务可用")
            return True
            
        except Exception as e:
            logger.warning(f"❌ SSH MCP检查失败: {e}")
            return False
    
    async def execute_remote_command(self, hostname: str, username: str, password: str,
                                   command: str, use_sudo: bool = False, timeout: int = 30) -> Dict[str, Any]:
        """执行远程命令"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过命令执行")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "execute_remote_command",
                "arguments": {
                    "hostname": hostname,
                    "username": username,
                    "password": password,
                    "command": command,
                    "use_sudo": use_sudo,
                    "timeout": timeout
                }
            }
            
            result = await self._call_mcp_tool(tool_call)
            return json.loads(result) if isinstance(result, str) else result
            
        except Exception as e:
            logger.error(f"执行远程命令失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def execute_service_operation(self, service_name: str, operation: str, 
                                      log_lines: int = 100) -> Dict[str, Any]:
        """执行服务操作"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过服务操作")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "execute_service_operation",
                "arguments": {
                    "service_name": service_name,
                    "operation": operation,
                    "log_lines": log_lines
                }
            }
            
            result = await self._call_mcp_tool(tool_call)
            return json.loads(result) if isinstance(result, str) else result
            
        except Exception as e:
            logger.error(f"执行服务操作失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """获取服务状态"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过获取服务状态")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "get_service_status",
                "arguments": {
                    "service_name": service_name
                }
            }
            
            result = await self._call_mcp_tool(tool_call)
            return json.loads(result) if isinstance(result, str) else result
            
        except Exception as e:
            logger.error(f"获取服务状态失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_service_logs(self, service_name: str, lines: int = 100) -> Dict[str, Any]:
        """获取服务日志"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过获取服务日志")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "get_service_logs",
                "arguments": {
                    "service_name": service_name,
                    "lines": lines
                }
            }
            
            result = await self._call_mcp_tool(tool_call)
            return json.loads(result) if isinstance(result, str) else result
            
        except Exception as e:
            logger.error(f"获取服务日志失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def list_services(self) -> Dict[str, Any]:
        """列出所有服务"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过列出服务")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "list_services",
                "arguments": {}
            }
            
            result = await self._call_mcp_tool(tool_call)
            return json.loads(result) if isinstance(result, str) else result
            
        except Exception as e:
            logger.error(f"列出服务失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_ssh_connection(self, hostname: str, username: str, password: str) -> Dict[str, Any]:
        """测试SSH连接"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过SSH连接测试")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "test_ssh_connection",
                "arguments": {
                    "hostname": hostname,
                    "username": username,
                    "password": password
                }
            }
            
            result = await self._call_mcp_tool(tool_call)
            return json.loads(result) if isinstance(result, str) else result
            
        except Exception as e:
            logger.error(f"测试SSH连接失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _call_mcp_tool(self, tool_call: Dict[str, Any]) -> str:
        """调用MCP工具"""
        try:
            # 切换到MCP目录
            original_cwd = os.getcwd()
            os.chdir(self.mcp_path)
            
            # 构造调用命令（使用当前Python环境）
            import sys
            python_executable = sys.executable
            cmd = [python_executable, "main.py"]
            
            # 构造完整的MCP会话
            # 1. 初始化请求
            init_request = json.dumps({
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "ssh-mcp-client",
                        "version": "1.0.0"
                    }
                }
            }) + "\n"
            
            # 2. 初始化完成通知
            initialized_notification = json.dumps({
                "jsonrpc": "2.0",
                "method": "notifications/initialized"
            }) + "\n"
            
            # 3. 工具调用请求
            tool_request = json.dumps({
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": tool_call["name"],
                    "arguments": tool_call["arguments"]
                }
            }) + "\n"
            
            # 组合所有输入
            input_data = init_request + initialized_notification + tool_request
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate(input=input_data.encode('utf-8'))
            try:
                stdout = stdout.decode('utf-8')
            except UnicodeDecodeError:
                stdout = stdout.decode('gbk', errors='ignore')
            try:
                stderr = stderr.decode('utf-8')
            except UnicodeDecodeError:
                stderr = stderr.decode('gbk', errors='ignore')
            
            # 恢复原目录
            os.chdir(original_cwd)
            
            if process.returncode == 0:
                logger.info(f"✅ SSH MCP工具调用成功: {tool_call['name']}")
                # 调试：打印原始输出
                logger.debug(f"原始stdout: {repr(stdout)}")
                
                # 解析MCP响应 - 查找工具调用的响应
                lines = stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        try:
                            response = json.loads(line)
                            # 查找ID为2的响应（工具调用响应）
                            if response.get("id") == 2:
                                if "result" in response:
                                    if "content" in response["result"]:
                                        content = response["result"]["content"]
                                        if isinstance(content, list) and len(content) > 0:
                                            return content[0].get("text", "")
                                    # 如果result中有isError=true，说明是错误响应
                                    elif response["result"].get("isError"):
                                        # 从content中提取错误信息
                                        content = response["result"].get("content", [])
                                        if isinstance(content, list) and len(content) > 0:
                                            error_text = content[0].get("text", "未知错误")
                                            return json.dumps({"success": False, "error": error_text})
                                elif "error" in response:
                                    return json.dumps(response)
                        except json.JSONDecodeError as e:
                            logger.debug(f"JSON解析错误 line: {repr(line)}, error: {e}")
                            continue
                
                return stdout
            else:
                logger.error(f"❌ SSH MCP工具调用失败: {stderr}")
                raise Exception(f"MCP工具调用失败: {stderr}")
                
        except Exception as e:
            logger.error(f"SSH MCP工具调用异常: {e}")
            # 确保恢复原目录
            try:
                os.chdir(original_cwd)
            except:
                pass
            raise
    
    async def test_connection(self) -> bool:
        """测试MCP连接"""
        if not self.enabled:
            return False
        
        try:
            result = await self.list_services()
            return "services" in result or "error" not in result
        except:
            return False


# 创建全局实例
ssh_mcp_client = SSHMCPClient()


# 为了向后兼容，提供一些便捷函数
async def execute_service_operation(service_name: str, operation: str, log_lines: int = 100) -> Dict[str, Any]:
    """执行服务操作（便捷函数）"""
    return await ssh_mcp_client.execute_service_operation(service_name, operation, log_lines)


async def get_service_status(service_name: str) -> Dict[str, Any]:
    """获取服务状态（便捷函数）"""
    return await ssh_mcp_client.get_service_status(service_name)


async def get_service_logs(service_name: str, lines: int = 100) -> Dict[str, Any]:
    """获取服务日志（便捷函数）"""
    return await ssh_mcp_client.get_service_logs(service_name, lines)


async def execute_remote_command(hostname: str, username: str, password: str,
                               command: str, use_sudo: bool = False, timeout: int = 30) -> Dict[str, Any]:
    """执行远程命令（便捷函数）"""
    return await ssh_mcp_client.execute_remote_command(hostname, username, password, command, use_sudo, timeout)


if __name__ == "__main__":
    # 测试代码
    async def test():
        # 测试列出服务
        result = await ssh_mcp_client.list_services()
        print("服务列表:", json.dumps(result, ensure_ascii=False, indent=2))
        
        # 测试获取服务状态
        if "services" in result and len(result["services"]) > 0:
            service_name = result["services"][0]["name"]
            status = await ssh_mcp_client.get_service_status(service_name)
            print(f"\n{service_name} 状态:", json.dumps(status, ensure_ascii=False, indent=2))
    
    asyncio.run(test()) 
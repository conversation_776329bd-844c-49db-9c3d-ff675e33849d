#!/usr/bin/env python3
"""
AI运维代理故障场景测试
模拟服务故障并验证自动修复流程
"""

import asyncio
import json
import time
from datetime import datetime
import requests
from loguru import logger
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

class FailureScenarioTester:
    """故障场景测试器"""
    
    def __init__(self):
        self.api_base_url = "http://localhost:8000"
        self.test_results = []
        
    def setup_logging(self):
        """设置日志"""
        logger.remove()
        logger.add(
            sys.stdout,
            level="INFO",
            format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
            colorize=True
        )
    
    def log_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        if success:
            logger.info(f"✅ {test_name}: {message}")
        else:
            logger.error(f"❌ {test_name}: {message}")
    
    def test_api_connectivity(self):
        """测试API连通性"""
        logger.info("🔌 测试API连通性...")
        
        try:
            response = requests.get(f"{self.api_base_url}/", timeout=10)
            if response.status_code == 200:
                self.log_test_result("API连通性", True, "API服务响应正常")
                return True
            else:
                self.log_test_result("API连通性", False, f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test_result("API连通性", False, str(e))
            return False
    
    def get_monitor_status(self):
        """获取监控状态"""
        logger.info("📊 获取监控状态...")
        
        try:
            response = requests.get(f"{self.api_base_url}/monitor/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    services = data.get("data", {}).get("services", [])
                    self.log_test_result("监控状态获取", True, f"监控中的服务数: {len(services)}")
                    
                    # 显示每个服务的状态
                    for service in services:
                        status_emoji = "🟢" if service.get("status") else "🔴"
                        logger.info(f"   {status_emoji} {service.get('service_name')}: {service.get('url')}")
                    
                    return services
                else:
                    self.log_test_result("监控状态获取", False, "监控服务响应失败")
                    return []
            else:
                self.log_test_result("监控状态获取", False, f"状态码: {response.status_code}")
                return []
        except Exception as e:
            self.log_test_result("监控状态获取", False, str(e))
            return []
    
    def trigger_manual_diagnosis(self, service_name: str, error_description: str):
        """手动触发诊断测试"""
        logger.info(f"🔍 触发手动诊断: {service_name}")
        
        try:
            payload = {
                "service_name": service_name,
                "server_ip": "localhost",
                "error_info": error_description,
                "max_attempts": 3  # 测试时只尝试3次
            }
            
            response = requests.post(
                f"{self.api_base_url}/continuous-diagnosis",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    session_id = data.get("data", {}).get("session_id")
                    self.log_test_result("手动诊断触发", True, f"诊断会话已启动: {session_id}")
                    return session_id
                else:
                    self.log_test_result("手动诊断触发", False, "诊断启动失败")
                    return None
            else:
                self.log_test_result("手动诊断触发", False, f"状态码: {response.status_code}")
                return None
        except Exception as e:
            self.log_test_result("手动诊断触发", False, str(e))
            return None
    
    def monitor_diagnosis_session(self, session_id: str, timeout: int = 180):
        """监控诊断会话进展"""
        logger.info(f"👀 监控诊断会话: {session_id}")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.api_base_url}/session/{session_id}", timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        session_data = data.get("data", {})
                        status = session_data.get("status")
                        current_attempt = session_data.get("current_attempt", 0)
                        
                        logger.info(f"   📈 会话状态: {status}, 尝试次数: {current_attempt}")
                        
                        # 检查是否完成
                        if status in ["resolved", "failed", "exhausted"]:
                            final_result = session_data.get("final_result", "无结果信息")
                            success = status == "resolved"
                            self.log_test_result("诊断会话完成", success, f"最终状态: {status}, 结果: {final_result}")
                            return success, status, final_result
                
                # 等待5秒后再次检查
                time.sleep(5)
                
            except Exception as e:
                logger.warning(f"   ⚠️ 获取会话状态失败: {e}")
                time.sleep(5)
        
        self.log_test_result("诊断会话监控", False, "监控超时")
        return False, "timeout", "监控超时"
    
    def test_wechat_notification(self):
        """测试微信通知功能"""
        logger.info("💬 测试微信通知...")
        
        try:
            payload = {
                "message_type": "text",
                "content": "🧪 AI运维代理故障场景测试 - 微信通知功能测试"
            }
            
            response = requests.post(
                f"{self.api_base_url}/wechat/notify",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.log_test_result("微信通知测试", True, "通知发送成功")
                    return True
                else:
                    self.log_test_result("微信通知测试", False, "通知发送失败")
                    return False
            else:
                self.log_test_result("微信通知测试", False, f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test_result("微信通知测试", False, str(e))
            return False
    
    def simulate_service_failure(self):
        """模拟服务故障场景"""
        logger.info("🚨 模拟服务故障场景...")
        
        # 场景1：nginx服务端口占用问题
        scenario1_description = """
服务故障报告:
- 服务名称: nginx
- 错误现象: 无法启动，端口80被占用
- 错误日志: bind() to 0.0.0.0:80 failed (98: Address already in use)
- 发生时间: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        session_id1 = self.trigger_manual_diagnosis("nginx", scenario1_description)
        
        if session_id1:
            logger.info("⏳ 等待诊断完成 (最多3分钟)...")
            success1, status1, result1 = self.monitor_diagnosis_session(session_id1, timeout=180)
            
            if success1:
                logger.info(f"🎉 场景1测试成功: {result1}")
            else:
                logger.warning(f"⚠️ 场景1测试完成，状态: {status1}")
        
        # 等待一段时间再进行下一个测试
        time.sleep(10)
        
        # 场景2：Docker服务停止问题
        scenario2_description = """
服务故障报告:
- 服务名称: docker-service
- 错误现象: 容器意外停止
- 错误日志: Container exited with code 1
- 可能原因: 内存不足或配置错误
- 发生时间: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        session_id2 = self.trigger_manual_diagnosis("docker-service", scenario2_description)
        
        if session_id2:
            logger.info("⏳ 等待第二个诊断完成...")
            success2, status2, result2 = self.monitor_diagnosis_session(session_id2, timeout=180)
            
            if success2:
                logger.info(f"🎉 场景2测试成功: {result2}")
            else:
                logger.warning(f"⚠️ 场景2测试完成，状态: {status2}")
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("=" * 60)
        logger.info("📊 故障场景测试报告")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["success"]])
        failed_tests = total_tests - passed_tests
        
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过: {passed_tests}")
        logger.info(f"失败: {failed_tests}")
        logger.info(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            logger.info("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    logger.info(f"   - {result['test_name']}: {result['message']}")
        
        logger.info("\n✅ 通过的测试:")
        for result in self.test_results:
            if result["success"]:
                logger.info(f"   - {result['test_name']}: {result['message']}")
        
        # 保存测试报告
        report_file = f"logs/failure_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "test_type": "failure_scenarios",
                "summary": {
                    "total_tests": total_tests,
                    "passed_tests": passed_tests,
                    "failed_tests": failed_tests,
                    "success_rate": passed_tests/total_tests*100
                },
                "results": self.test_results
            }, f, ensure_ascii=False, indent=2)
        
        logger.info(f"\n📄 详细测试报告已保存到: {report_file}")
    
    def run_all_tests(self):
        """运行所有故障场景测试"""
        logger.info("🚀 开始故障场景测试...")
        logger.info("=" * 60)
        
        # 1. 检查基础连通性
        if not self.test_api_connectivity():
            logger.error("❌ API连通性测试失败，无法继续测试")
            return
        
        # 2. 获取监控状态
        services = self.get_monitor_status()
        
        # 3. 测试微信通知
        self.test_wechat_notification()
        
        # 4. 模拟故障场景
        self.simulate_service_failure()
        
        # 5. 生成测试报告
        self.generate_test_report()

def main():
    """主函数"""
    tester = FailureScenarioTester()
    tester.setup_logging()
    
    logger.info("🔬 AI运维代理故障场景测试工具")
    logger.info("将模拟各种服务故障并验证自动修复能力")
    logger.info("请确保AI运维代理主服务已启动 (python main.py)")
    
    # 等待用户确认
    input("\n按回车键开始测试...")
    
    tester.run_all_tests()

if __name__ == "__main__":
    main() 
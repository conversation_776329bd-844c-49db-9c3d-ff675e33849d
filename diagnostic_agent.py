import json
import asyncio
from typing import Dict, List, Optional, Any
from loguru import logger
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from model_client import aiops_client
# 更新导入：使用SSH MCP客户端替代原SSH客户端
from ssh_mcp_client import ssh_mcp_client
from wechat_mcp_client import wechat_mcp_client

class DiagnosticStatus(Enum):
    """诊断状态枚举"""
    PENDING = "pending"           # 等待中
    DIAGNOSING = "diagnosing"     # 诊断中
    EXECUTING = "executing"       # 执行中
    RESOLVED = "resolved"         # 已解决
    FAILED = "failed"             # 失败
    EXHAUSTED = "exhausted"       # 方案已用尽

@dataclass
class DiagnosticAttempt:
    """单次诊断尝试记录"""
    attempt_number: int
    timestamp: datetime
    diagnosis: str
    solution: str
    commands: List[str]
    execution_results: List[Dict]
    success: bool
    error_message: Optional[str] = None

@dataclass
class ContinuousDiagnosticSession:
    """持续诊断会话"""
    session_id: str
    service_name: str
    server_ip: str
    initial_error: str
    status: DiagnosticStatus
    start_time: datetime
    attempts: List[DiagnosticAttempt] = field(default_factory=list)
    max_attempts: int = 10
    current_attempt: int = 0
    final_result: Optional[str] = None
    resolution_time: Optional[datetime] = None
    
    def is_active(self) -> bool:
        """检查会话是否仍在活跃状态"""
        return self.status in [DiagnosticStatus.PENDING, DiagnosticStatus.DIAGNOSING, DiagnosticStatus.EXECUTING]

class DiagnosticAgent:
    """增强的AI诊断代理，支持持续诊断直到问题解决"""
    
    def __init__(self):
        self.active_sessions: Dict[str, ContinuousDiagnosticSession] = {}
        self.diagnostic_history = []
        self.global_attempt_strategies = [
            "restart_service",      # 重启服务
            "check_dependencies",   # 检查依赖
            "clean_rebuild",       # 清理重建
            "resource_check",      # 资源检查
            "config_validation",   # 配置验证
            "network_diagnosis",   # 网络诊断
            "rollback_attempt",    # 回滚尝试
            "emergency_recovery"   # 紧急恢复
        ]
    
    async def start_continuous_diagnosis(self, service_name: str, server_ip: str, 
                                       error_info: str, max_attempts: int = 10) -> str:
        """开始持续诊断会话"""
        session_id = f"{service_name}_{server_ip}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        session = ContinuousDiagnosticSession(
            session_id=session_id,
            service_name=service_name,
            server_ip=server_ip,
            initial_error=error_info,
            status=DiagnosticStatus.PENDING,
            start_time=datetime.now(),
            max_attempts=max_attempts
        )
        
        self.active_sessions[session_id] = session
        logger.info(f"启动持续诊断会话: {session_id}")
        
        # 发送企业微信通知（诊断开始）
        asyncio.create_task(wechat_mcp_client.send_diagnosis_update(
            session_id, service_name, server_ip, "starting", "开始持续诊断", 0
        ))
        
        # 立即开始第一次诊断
        asyncio.create_task(self._continuous_diagnosis_loop(session_id))
        
        return session_id
    
    async def _continuous_diagnosis_loop(self, session_id: str):
        """持续诊断循环"""
        session = self.active_sessions.get(session_id)
        if not session:
            return
        
        logger.info(f"开始持续诊断循环: {session_id}")
        
        while session.is_active() and session.current_attempt < session.max_attempts:
            try:
                session.status = DiagnosticStatus.DIAGNOSING
                session.current_attempt += 1
                
                logger.info(f"会话 {session_id} - 第 {session.current_attempt} 次尝试")
                
                # 执行单次诊断尝试
                attempt_result = await self._perform_single_diagnosis_attempt(session)
                session.attempts.append(attempt_result)
                
                # 检查是否成功解决
                if attempt_result.success:
                    # 验证服务是否真正恢复
                    if await self._verify_service_recovery(session):
                        session.status = DiagnosticStatus.RESOLVED
                        session.resolution_time = datetime.now()
                        session.final_result = f"问题已在第 {session.current_attempt} 次尝试中解决"
                        logger.info(f"会话 {session_id} - 问题已解决！")
                        break
                    else:
                        logger.warning(f"会话 {session_id} - 命令执行成功但服务仍有问题，继续诊断")
                
                # 等待一段时间再进行下次尝试
                await asyncio.sleep(30)  # 30秒间隔
                
            except Exception as e:
                logger.error(f"会话 {session_id} - 诊断循环出错: {e}")
                # 记录错误但继续尝试
                error_attempt = DiagnosticAttempt(
                    attempt_number=session.current_attempt,
                    timestamp=datetime.now(),
                    diagnosis=f"诊断过程出错: {str(e)}",
                    solution="系统错误，将尝试其他方案",
                    commands=[],
                    execution_results=[],
                    success=False,
                    error_message=str(e)
                )
                session.attempts.append(error_attempt)
                await asyncio.sleep(15)  # 错误后短暂等待
        
        # 诊断循环结束
        if session.status != DiagnosticStatus.RESOLVED:
            if session.current_attempt >= session.max_attempts:
                session.status = DiagnosticStatus.EXHAUSTED
                session.final_result = f"已尝试 {session.max_attempts} 次，所有方案均已用尽"
            else:
                session.status = DiagnosticStatus.FAILED
                session.final_result = "诊断过程异常终止"
        
        # 生成最终报告
        await self._generate_final_report(session)
        logger.info(f"会话 {session_id} 结束，状态: {session.status.value}")
        
        # 发送诊断完成通知
        success = session.status == DiagnosticStatus.RESOLVED
        await wechat_mcp_client.send_diagnosis_update(
            session.session_id,
            session.service_name,
            session.server_ip,
            session.status.value,
            session.final_result,
            session.current_attempt
        )
    
    async def _perform_single_diagnosis_attempt(self, session: ContinuousDiagnosticSession) -> DiagnosticAttempt:
        """执行单次诊断尝试"""
        attempt_start = datetime.now()
        
        try:
            # 1. 使用SSH MCP客户端获取服务列表
            services_result = await ssh_mcp_client.list_services()
            
            # 查找指定的服务
            service_info = None
            if services_result.get("success", False) and "services" in services_result:
                for service in services_result["services"]:
                    if service["name"] == session.service_name:
                        service_info = service
                        break
            
            if not service_info:
                return DiagnosticAttempt(
                    attempt_number=session.current_attempt,
                    timestamp=attempt_start,
                    diagnosis="未找到服务配置信息",
                    solution="请检查service_info.txt文件",
                    commands=[],
                    execution_results=[],
                    success=False,
                    error_message="服务信息不存在"
                )
            
            # 2. 收集当前状态信息
            status_info, logs = await self._collect_service_info(
                session.server_ip, service_info, session.service_name
            )
            
            # 3. 构建诊断上下文（包含历史尝试）
            diagnosis_context = self._build_diagnosis_context(session, status_info, logs)
            
            # 4. 使用本地模型进行智能诊断 - 给AI更大的自主权
            diagnostic_response = await aiops_client.diagnose_error_with_context(
                session.service_name, 
                session.server_ip, 
                session.initial_error,
                diagnosis_context,
                session.current_attempt,
                # 移除策略限制，让AI自主决定
                None
            )
            
            if not diagnostic_response:
                # 如果AI完全无法响应，才使用后备策略
                logger.warning(f"AI诊断无响应，使用后备策略")
                return await self._use_fallback_strategy(session, service_info)
            
            # 5. 解析诊断结果
            try:
                diagnostic_data = json.loads(diagnostic_response)
            except json.JSONDecodeError:
                diagnostic_data = {
                    "diagnosis": diagnostic_response,
                    "solution": f"尝试第 {session.current_attempt} 种方案",
                    "commands": await self._generate_fallback_commands(session, service_info)
                }
            
            # 6. 执行修复命令
            session.status = DiagnosticStatus.EXECUTING
            execution_results = await self._execute_fix_commands(
                session.server_ip, service_info, 
                diagnostic_data.get("commands", [])
            )
            
            # 7. 评估执行结果
            success = len(execution_results) > 0 and all(r.get("success", False) for r in execution_results)
            
            return DiagnosticAttempt(
                attempt_number=session.current_attempt,
                timestamp=attempt_start,
                diagnosis=diagnostic_data.get("diagnosis", "未知问题"),
                solution=diagnostic_data.get("solution", "无解决方案"),
                commands=diagnostic_data.get("commands", []),
                execution_results=execution_results,
                success=success
            )
            
        except Exception as e:
            logger.error(f"单次诊断尝试失败: {e}")
            return DiagnosticAttempt(
                attempt_number=session.current_attempt,
                timestamp=attempt_start,
                diagnosis=f"诊断尝试失败: {str(e)}",
                solution="将尝试其他方案",
                commands=[],
                execution_results=[],
                success=False,
                error_message=str(e)
            )
    
    def _build_diagnosis_context(self, session: ContinuousDiagnosticSession, 
                               status_info: str, logs: str) -> str:
        """构建包含历史尝试的诊断上下文"""
        context = f"""
=== 当前状态信息 ===
服务状态: {status_info}
最新日志: {logs}

=== 历史尝试记录 ===
"""
        for attempt in session.attempts:
            context += f"""
尝试 {attempt.attempt_number}:
- 诊断: {attempt.diagnosis}
- 解决方案: {attempt.solution}
- 执行命令: {attempt.commands}
- 结果: {'成功' if attempt.success else '失败'}
"""
            if attempt.error_message:
                context += f"- 错误: {attempt.error_message}\n"
        
        context += f"\n=== 当前是第 {session.current_attempt} 次尝试，请提供新的诊断和解决方案 ==="
        return context
    
    async def _use_fallback_strategy(self, session: ContinuousDiagnosticSession, 
                                   service_info: Dict) -> DiagnosticAttempt:
        """使用预定义的后备策略"""
        strategy_index = min(session.current_attempt - 1, len(self.global_attempt_strategies) - 1)
        strategy = self.global_attempt_strategies[strategy_index]
        
        commands = await self._generate_fallback_commands(session, service_info)
        
        # 执行后备策略命令
        execution_results = await self._execute_fix_commands(
            session.server_ip, service_info, commands
        )
        
        success = len(execution_results) > 0 and all(r.get("success", False) for r in execution_results)
        
        return DiagnosticAttempt(
            attempt_number=session.current_attempt,
            timestamp=datetime.now(),
            diagnosis=f"使用预定义策略: {strategy}",
            solution=f"执行第 {session.current_attempt} 种标准修复方案",
            commands=commands,
            execution_results=execution_results,
            success=success
        )
    
    async def _generate_fallback_commands(self, session: ContinuousDiagnosticSession, 
                                        service_info: Dict) -> List[str]:
        """生成后备命令（支持docker-compose和supervisor）"""
        strategy_index = min(session.current_attempt - 1, len(self.global_attempt_strategies) - 1)
        strategy = self.global_attempt_strategies[strategy_index]
        service_type = service_info.get("service_type", "docker-compose")
        directory = service_info.get("directory", "/opt")
        service_name = session.service_name
        
        if service_type == "docker-compose":
            strategy_commands = {
                "restart_service": [
                    f"sudo bash -c \"cd {directory} && docker-compose stop\"",
                    f"sudo bash -c \"cd {directory} && docker-compose start\""
                ],
                "check_dependencies": [
                    f"sudo bash -c \"cd {directory} && docker-compose ps\"",
                    "sudo docker images",
                    "df -h",
                    "free -h"
                ],
                "clean_rebuild": [
                    f"sudo bash -c \"cd {directory} && docker-compose down\"",
                    "sudo docker system prune -f",
                    f"sudo bash -c \"cd {directory} && docker-compose up -d\""
                ],
                "resource_check": [
                    "sudo docker stats --no-stream",
                    "sudo docker system df",
                    f"sudo bash -c \"cd {directory} && docker-compose logs --tail=50\""
                ],
                "config_validation": [
                    f"sudo bash -c \"cd {directory} && docker-compose config\"",
                    f"sudo bash -c \"cd {directory} && docker-compose ps\"",
                    f"sudo bash -c \"cd {directory} && docker-compose logs --tail=100\""
                ],
                "network_diagnosis": [
                    "sudo docker network ls",
                    f"sudo bash -c \"cd {directory} && docker-compose down\"",
                    "sudo docker network prune -f",
                    f"sudo bash -c \"cd {directory} && docker-compose up -d\""
                ],
                "rollback_attempt": [
                    f"sudo bash -c \"cd {directory} && docker-compose down\"",
                    f"bash -c \"cd {directory} && git status\"",
                    f"sudo bash -c \"cd {directory} && docker-compose up -d\""
                ],
                "emergency_recovery": [
                    f"sudo bash -c \"cd {directory} && docker-compose down --remove-orphans\"",
                    "sudo docker system prune -af",
                    "sudo docker volume prune -f",
                    f"sudo bash -c \"cd {directory} && docker-compose pull\"",
                    f"sudo bash -c \"cd {directory} && docker-compose up -d\""
                ]
            }
            default_commands = [f"sudo bash -c \"cd {directory} && docker-compose restart\""]
            
        elif service_type == "supervisor":
            # 使用正确的supervisorctl路径
            supervisorctl_cmd = "sudo /home/<USER>/anaconda3/bin/supervisorctl"
            
            strategy_commands = {
                "restart_service": [
                    f"{supervisorctl_cmd} stop {service_name}",
                    f"{supervisorctl_cmd} start {service_name}"
                ],
                "check_dependencies": [
                    f"{supervisorctl_cmd} status {service_name}",
                    f"{supervisorctl_cmd} status",
                    "ps aux | grep python",
                    "df -h",
                    "free -h"
                ],
                "clean_rebuild": [
                    f"{supervisorctl_cmd} stop {service_name}",
                    f"{supervisorctl_cmd} reread",
                    f"{supervisorctl_cmd} update",
                    f"{supervisorctl_cmd} start {service_name}"
                ],
                "resource_check": [
                    f"{supervisorctl_cmd} status {service_name}",
                    f"sudo tail -n 100 /var/log/supervisor/{service_name}.log",
                    "ps aux | grep supervisor",
                    "df -h",
                    "free -h"
                ],
                "config_validation": [
                    f"{supervisorctl_cmd} status {service_name}",
                    f"{supervisorctl_cmd} reread",
                    f"sudo tail -n 200 /var/log/supervisor/{service_name}.log"
                ],
                "network_diagnosis": [
                    f"{supervisorctl_cmd} status {service_name}",
                    "netstat -tlnp",
                    f"{supervisorctl_cmd} restart {service_name}"
                ],
                "rollback_attempt": [
                    f"{supervisorctl_cmd} stop {service_name}",
                    f"bash -c \"cd {directory} && git status\"",
                    f"{supervisorctl_cmd} start {service_name}"
                ],
                "emergency_recovery": [
                    f"{supervisorctl_cmd} stop {service_name}",
                    "sudo pkill -f supervisor",
                    "sudo systemctl restart supervisor",
                    f"{supervisorctl_cmd} start {service_name}"
                ]
            }
            default_commands = [f"{supervisorctl_cmd} restart {service_name}"]
        else:
            # 未支持的服务类型，返回基本命令
            return [f"echo '不支持的服务类型: {service_type}'"]
        
        return strategy_commands.get(strategy, default_commands)

    async def _verify_service_recovery(self, session: ContinuousDiagnosticSession) -> bool:
        """验证服务是否真正恢复"""
        try:
            # 等待服务启动
            await asyncio.sleep(10)
            
            # 使用SSH MCP客户端获取服务状态
            status_result = await ssh_mcp_client.get_service_status(session.service_name)
            
            # 检查是否正在运行
            is_running = status_result.get("success", False)
            
            if is_running:
                # 进一步验证 - 检查最新日志是否有错误
                logs_result = await ssh_mcp_client.get_service_logs(session.service_name, lines=10)
                
                if logs_result.get("success", False):
                    logs = logs_result.get("stdout", "")
                    
                    # 简单的错误检测
                    error_indicators = ["error", "failed", "exception", "traceback", "fatal"]
                    recent_errors = any(indicator.lower() in logs.lower() for indicator in error_indicators)
                    
                    return not recent_errors
            
            return False
            
        except Exception as e:
            logger.error(f"验证服务恢复时出错: {e}")
            return False

    async def _generate_final_report(self, session: ContinuousDiagnosticSession):
        """生成最终诊断报告"""
        report = {
            "session_id": session.session_id,
            "service_name": session.service_name,
            "server_ip": session.server_ip,
            "initial_error": session.initial_error,
            "status": session.status.value,
            "start_time": session.start_time.isoformat(),
            "resolution_time": session.resolution_time.isoformat() if session.resolution_time else None,
            "total_attempts": len(session.attempts),
            "final_result": session.final_result,
            "attempts": [
                {
                    "attempt": attempt.attempt_number,
                    "timestamp": attempt.timestamp.isoformat(),
                    "diagnosis": attempt.diagnosis,
                    "solution": attempt.solution,
                    "commands": attempt.commands,
                    "success": attempt.success,
                    "error": attempt.error_message
                }
                for attempt in session.attempts
            ]
        }
        
        # 保存到历史记录
        self.diagnostic_history.append(report)
        
        # 记录日志
        logger.info(f"最终报告生成完成: {session.session_id}")
        logger.info(f"报告摘要: {session.final_result}")
    
    def get_session_status(self, session_id: str) -> Optional[Dict]:
        """获取诊断会话状态"""
        session = self.active_sessions.get(session_id)
        if not session:
            # 检查历史记录
            for history in self.diagnostic_history:
                if history.get("session_id") == session_id:
                    return history
            return None
        
        return {
            "session_id": session.session_id,
            "service_name": session.service_name,
            "server_ip": session.server_ip,
            "status": session.status.value,
            "current_attempt": session.current_attempt,
            "max_attempts": session.max_attempts,
            "start_time": session.start_time.isoformat(),
            "is_active": session.is_active(),
            "attempts_count": len(session.attempts),
            "latest_diagnosis": session.attempts[-1].diagnosis if session.attempts else None
        }
    
    def stop_session(self, session_id: str) -> bool:
        """停止诊断会话"""
        session = self.active_sessions.get(session_id)
        if session and session.is_active():
            session.status = DiagnosticStatus.FAILED
            session.final_result = "会话被手动停止"
            logger.info(f"会话 {session_id} 被手动停止")
            return True
        return False
    
    def list_active_sessions(self) -> List[Dict]:
        """列出所有活跃的诊断会话"""
        return [
            {
                "session_id": session.session_id,
                "service_name": session.service_name,
                "server_ip": session.server_ip,
                "status": session.status.value,
                "current_attempt": session.current_attempt,
                "start_time": session.start_time.isoformat()
            }
            for session in self.active_sessions.values()
            if session.is_active()
        ]

    async def _collect_service_info(self, server_ip: str, service_info: Dict[str, str], 
                                   service_name: str) -> tuple[str, str]:
        """收集服务状态和日志信息"""
        try:
            # 使用SSH MCP客户端获取服务状态
            status_result = await ssh_mcp_client.get_service_status(service_name)
            
            # 获取服务日志
            logs_result = await ssh_mcp_client.get_service_logs(service_name, lines=50)
            
            # 处理状态结果
            if status_result.get("success", False):
                status_str = json.dumps(status_result, ensure_ascii=False, indent=2)
            else:
                status_str = f"状态获取失败: {status_result.get('error', '未知错误')}"
            
            # 处理日志结果
            if logs_result.get("success", False):
                logs = logs_result.get("stdout", "无日志输出")
            else:
                logs = f"日志获取失败: {logs_result.get('error', '未知错误')}"
            
            return status_str, logs
            
        except Exception as e:
            logger.error(f"收集服务信息失败: {e}")
            return f"状态收集失败: {e}", f"日志收集失败: {e}"
    
    async def _execute_fix_commands(self, server_ip: str, service_info: Dict[str, str],
                                   commands: List[str]) -> List[Dict]:
        """执行修复命令"""
        results = []
        
        try:
            for command in commands:
                logger.info(f"执行修复命令: {command}")
                
                # 使用SSH MCP客户端执行远程命令
                result = await ssh_mcp_client.execute_remote_command(
                    hostname=server_ip,
                    username=service_info["username"],
                    password=service_info["password"],
                    command=command,
                    use_sudo=not command.strip().startswith("sudo"),
                    timeout=60
                )
                
                results.append(result)
                logger.info(f"命令执行结果: {result.get('success', False)}")
                
                # 如果关键命令失败，可能需要停止后续执行
                is_critical_command = (
                    ("docker" in command.lower() and any(op in command for op in ["up", "start", "restart"])) or
                    ("supervisorctl" in command.lower() and any(op in command for op in ["start", "restart"]))
                )
                
                if not result.get("success", False) and is_critical_command:
                    logger.warning(f"关键服务命令失败，停止后续执行")
                    # 仅在严重错误且微信通知启用时发送警报
                    asyncio.create_task(wechat_mcp_client.send_system_alert(
                        "error",
                        service_info.get("service_name", "未知服务"),
                        server_ip,
                        f"关键命令执行失败: {command}"
                    ))
                    break
                
                # 在命令之间添加短暂延迟
                await asyncio.sleep(2)
                
        except Exception as e:
            logger.error(f"执行修复命令时出错: {e}")
            results.append({
                "command": "execution_error",
                "success": False,
                "error": str(e)
            })
        
        return results
    
    async def get_service_health_check(self, service_name: str, server_ip: str) -> Dict:
        """获取服务健康检查结果"""
        try:
            # 使用SSH MCP客户端获取服务状态
            status_result = await ssh_mcp_client.get_service_status(service_name)
            
            # 获取服务日志
            logs_result = await ssh_mcp_client.get_service_logs(service_name, lines=20)
            
            # 检查服务是否异常
            is_running = status_result.get("success", False)
            if not is_running and wechat_mcp_client.enabled:
                # 仅在服务异常且启用微信通知时发送警报
                asyncio.create_task(wechat_mcp_client.send_system_alert(
                    "error",
                    service_name,
                    server_ip,
                    "服务未正常运行，健康检查失败"
                ))
            
            return {
                "success": True,
                "service_name": service_name,
                "server_ip": server_ip,
                "status": status_result,
                "recent_logs": logs_result.get("stdout", "无日志"),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def get_diagnostic_history(self, limit: int = 10) -> List[Dict]:
        """获取诊断历史记录"""
        history = self.diagnostic_history[-limit:] if limit > 0 else self.diagnostic_history
        
        return [
            {
                "session_id": item.get("session_id", "unknown"),
                "service_name": item.get("service_name", "unknown"),
                "server_ip": item.get("server_ip", "unknown"),
                "initial_error": item.get("initial_error", ""),
                "status": item.get("status", "unknown"),
                "start_time": item.get("start_time", ""),
                "total_attempts": item.get("total_attempts", 0),
                "final_result": item.get("final_result", "")
            }
            for item in history
        ]

# 全局诊断代理实例
diagnostic_agent = DiagnosticAgent() 